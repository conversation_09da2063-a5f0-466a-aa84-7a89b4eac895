// الحصول على مراجع للعناصر
const userNameElement = document.getElementById('user-name');
const userFullNameElement = document.getElementById('user-full-name');
const userAgeElement = document.getElementById('user-age');
const userCountryElement = document.getElementById('user-country');
const userInterestsElement = document.getElementById('user-interests');
const userRegistrationDateElement = document.getElementById('user-registration-date');
const logoutBtn = document.getElementById('logout-btn');

// التحقق من حالة تسجيل الدخول
const userEmail = window.localStorage.getItem('userEmail');
if (userEmail) {
    // المستخدم مسجل الدخول، عرض معلوماته
    userNameElement.textContent = userEmail;

    // جلب بيانات المستخدم من قاعدة البيانات
    // عرض رسالة تحميل
    userFullNameElement.textContent = 'جاري التحميل...';
    userAgeElement.textContent = 'جاري التحميل...';
    userCountryElement.textContent = 'جاري التحميل...';
    userInterestsElement.textContent = 'جاري التحميل...';
    userRegistrationDateElement.textContent = 'جاري التحميل...';

    // استدعاء دالة جلب بيانات المستخدم
    getUserData(userEmail)
        .then(userData => {
            // عرض بيانات المستخدم
            userFullNameElement.textContent = userData.fullName || 'غير متوفر';
            userAgeElement.textContent = userData.age || 'غير متوفر';
            userCountryElement.textContent = userData.country || 'غير متوفر';
            userInterestsElement.textContent = userData.interests || 'غير متوفر';
            userRegistrationDateElement.textContent = userData.registrationDate || 'غير متوفر';
        })
        .catch(error => {
            console.error('خطأ في جلب بيانات المستخدم:', error);

            // عرض رسالة خطأ
            userFullNameElement.textContent = 'غير متوفر';
            userAgeElement.textContent = 'غير متوفر';
            userCountryElement.textContent = 'غير متوفر';
            userInterestsElement.textContent = 'غير متوفر';
            userRegistrationDateElement.textContent = 'غير متوفر';
        });
} else {
    // المستخدم غير مسجل الدخول، توجيه إلى صفحة تسجيل الدخول
    window.location.href = 'index.html';
}

// إضافة مستمع حدث لزر تسجيل الخروج
logoutBtn.addEventListener('click', function() {
    // مسح بيانات المستخدم من التخزين المحلي
    window.localStorage.removeItem('userEmail');

    // توجيه المستخدم إلى صفحة تسجيل الدخول
    window.location.href = 'index.html';
});
