// الحصول على مراجع للعناصر
const addCourseForm = document.getElementById('add-course-form');
const titleInput = document.getElementById('title');
const descriptionInput = document.getElementById('description');
const instructorInput = document.getElementById('instructor');
const durationInput = document.getElementById('duration');
const levelInput = document.getElementById('level');
const priceInput = document.getElementById('price');
const cancelBtn = document.getElementById('cancel-btn');
const successMessage = document.getElementById('success-message');
const errorMessage = document.getElementById('error-message');

// التحقق من حالة تسجيل الدخول
const userEmail = window.localStorage.getItem('userEmail');
if (!userEmail) {
    // المستخدم غير مسجل الدخول، توجيه إلى صفحة تسجيل الدخول
    window.location.href = 'index.html';
}

// إضافة مستمع حدث لزر الإلغاء
cancelBtn.addEventListener('click', function() {
    window.location.href = 'courses.html';
});

// إضافة مستمع حدث لنموذج إضافة الكورس
addCourseForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // إخفاء رسائل النجاح والخطأ
    successMessage.style.display = 'none';
    errorMessage.style.display = 'none';
    
    // التحقق من صحة البيانات
    if (!titleInput.value.trim()) {
        showError('يرجى إدخال عنوان الكورس');
        return;
    }
    
    // جمع بيانات الكورس
    const courseData = {
        title: titleInput.value.trim(),
        description: descriptionInput.value.trim(),
        instructor: instructorInput.value.trim(),
        duration: durationInput.value.trim(),
        level: levelInput.value,
        price: priceInput.value.trim() || 'مجاني'
    };
    
    // إضافة الكورس
    addCourse(courseData)
        .then(response => {
            console.log('تم إضافة الكورس بنجاح:', response);
            
            // عرض رسالة النجاح
            showSuccess('تم إضافة الكورس بنجاح!');
            
            // إعادة تعيين النموذج
            addCourseForm.reset();
            
            // توجيه المستخدم إلى صفحة الكورسات بعد ثانيتين
            setTimeout(() => {
                window.location.href = 'courses.html';
            }, 2000);
        })
        .catch(error => {
            console.error('خطأ في إضافة الكورس:', error);
            
            // عرض رسالة الخطأ
            showError(error.message || 'حدث خطأ أثناء إضافة الكورس. يرجى المحاولة مرة أخرى.');
        });
});

// دالة لعرض رسالة نجاح
function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = 'block';
}

// دالة لعرض رسالة خطأ
function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
}
