// الحصول على مراجع للعناصر
const loginForm = document.getElementById('login-form');
const emailInput = document.getElementById('email');
const errorMessage = document.getElementById('error-message');

// التحقق مما إذا كان المستخدم مسجل الدخول بالفعل
firebase.auth().onAuthStateChanged(function(user) {
    if (user) {
        // المستخدم مسجل الدخول، توجيه إلى صفحة الترحيب
        window.location.href = 'welcome.html';
    }
});

// التحقق مما إذا كان المستخدم قد وصل عبر رابط تسجيل الدخول
if (firebase.auth().isSignInWithEmailLink(window.location.href)) {
    // الحصول على البريد الإلكتروني من التخزين المحلي
    let email = window.localStorage.getItem('emailForSignIn');

    // إذا لم يتم العثور على البريد الإلكتروني، اطلب من المستخدم إدخاله
    if (!email) {
        email = window.prompt('يرجى إدخال بريدك الإلكتروني للتأكيد:');
    }

    // إظهار رسالة تحميل
    errorMessage.textContent = 'جاري تسجيل الدخول...';
    errorMessage.style.color = '#4285f4';

    // إكمال عملية تسجيل الدخول
    firebase.auth().signInWithEmailLink(email, window.location.href)
        .then((result) => {
            // مسح البريد الإلكتروني من التخزين المحلي
            window.localStorage.removeItem('emailForSignIn');

            // توجيه المستخدم إلى صفحة الترحيب
            window.location.href = 'welcome.html';
        })
        .catch((error) => {
            // حدث خطأ أثناء تسجيل الدخول
            console.error('خطأ في تسجيل الدخول بالرابط:', error);

            // عرض رسالة الخطأ
            errorMessage.textContent = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.';
            errorMessage.style.color = '#d93025';
        });
}

// إضافة مستمع حدث لنموذج تسجيل الدخول
loginForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const email = emailInput.value;

    // التحقق من صحة البريد الإلكتروني
    if (!email || !email.includes('@')) {
        errorMessage.textContent = 'يرجى إدخال بريد إلكتروني صالح';
        errorMessage.style.color = '#d93025';
        return;
    }

    // إظهار رسالة تحميل
    errorMessage.textContent = 'جاري إرسال رابط تسجيل الدخول إلى بريدك الإلكتروني...';
    errorMessage.style.color = '#4285f4';

    // تسجيل الدخول بالبريد الإلكتروني فقط باستخدام الخادم الوسيط
    console.log('محاولة تسجيل الدخول بالبريد الإلكتروني:', email);

    // عرض رسالة تحميل
    errorMessage.textContent = 'جاري تسجيل الدخول...';
    errorMessage.style.color = '#4285f4';

    // استدعاء دالة تسجيل الدخول
    loginWithEmail(email)
        .then(data => {
            console.log('تم تسجيل الدخول بنجاح:', data);

            // تخزين البريد الإلكتروني في التخزين المحلي
            window.localStorage.setItem('userEmail', email);

            // عرض رسالة نجاح
            errorMessage.textContent = 'تم تسجيل الدخول بنجاح!';
            errorMessage.style.color = 'green';

            // توجيه المستخدم إلى صفحة الترحيب بعد ثانيتين
            setTimeout(() => {
                window.location.href = 'welcome.html';
            }, 2000);
        })
        .catch(error => {
            console.error('خطأ في تسجيل الدخول:', error);

            // عرض رسالة الخطأ
            errorMessage.textContent = error.message || 'فشل تسجيل الدخول. يرجى المحاولة مرة أخرى.';
            errorMessage.style.color = '#d93025';
        });
});
