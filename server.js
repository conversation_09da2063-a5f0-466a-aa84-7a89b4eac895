// خادم وسيط لتمرير الطلبات إلى Firebase
const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const app = express();
const PORT = 3000;

// تكوين Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDiCkboWAjaRQrBrHCcI1biQr8GfjgsZn0",
  authDomain: "e-teaching-8cba6.firebaseapp.com",
  databaseURL: "https://e-teaching-8cba6.firebaseio.com",
  projectId: "e-teaching-8cba6",
  storageBucket: "e-teaching-8cba6.appspot.com",
  messagingSenderId: "781763310298",
  appId: "1:781763310298:web:d79f584e3c0cdfd3f6abe3",
  measurementId: "G-L411M6CLDW"
};

// تمكين CORS لجميع الطلبات
app.use(cors());
app.use(express.json());

// مسار للحصول على بيانات المستخدم
app.get('/api/users/:email', async (req, res) => {
  try {
    const email = req.params.email;
    const projectId = firebaseConfig.projectId;
    const apiKey = firebaseConfig.apiKey;

    console.log(`محاولة جلب بيانات المستخدم: ${email}`);

    // نستمر في جلب البيانات من قاعدة البيانات الحقيقية

    // بناء عنوان URL للوصول إلى مجموعة المستخدمين في Firestore
    const encodedEmail = encodeURIComponent(email);
    const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/users/${encodedEmail}`;

    // إرسال طلب للحصول على بيانات المستخدم
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`فشل الاتصال بقاعدة البيانات: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('خطأ في جلب بيانات المستخدم:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لحفظ بيانات المستخدم
app.post('/api/users/:email', async (req, res) => {
  try {
    const email = req.params.email;
    const userData = req.body;
    const projectId = firebaseConfig.projectId;
    const apiKey = firebaseConfig.apiKey;

    console.log(`محاولة حفظ بيانات المستخدم: ${email}`);

    // بناء عنوان URL للوصول إلى مجموعة المستخدمين في Firestore
    const encodedEmail = encodeURIComponent(email);
    const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/users/${encodedEmail}`;

    // إرسال طلب لحفظ بيانات المستخدم
    const response = await fetch(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw new Error(`فشل حفظ بيانات المستخدم: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('خطأ في حفظ بيانات المستخدم:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لتسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'البريد الإلكتروني مطلوب' });
    }

    console.log(`محاولة تسجيل الدخول: ${email}`);

    // التحقق من صحة البريد الإلكتروني
    if (!email.includes('@')) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني غير صالح'
      });
    }

    // محاولة البحث عن المستخدم في قاعدة البيانات
    try {
      const encodedEmail = encodeURIComponent(email);
      const url = `https://firestore.googleapis.com/v1/projects/${firebaseConfig.projectId}/databases/(default)/documents/users/${encodedEmail}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // إذا كان المستخدم موجودًا، نعيد استجابة نجاح
      if (response.ok) {
        return res.json({
          success: true,
          email: email,
          message: 'تم تسجيل الدخول بنجاح'
        });
      } else {
        // إذا لم يكن المستخدم موجودًا، نعيد استجابة فشل
        return res.status(401).json({
          success: false,
          message: 'البريد الإلكتروني غير مسجل في النظام'
        });
      }
    } catch (error) {
      console.error('خطأ في البحث عن المستخدم:', error);

      // في حالة الفشل، نعيد استجابة فشل
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء البحث عن المستخدم. يرجى المحاولة مرة أخرى.'
      });
    }
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار للحصول على جميع البيانات من المجموعات الحقيقية فقط
app.get('/api/all-data', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;

    console.log(`🔍 البحث عن المجموعات الحقيقية في قاعدة البيانات...`);

    // أولاً: محاولة الحصول على قائمة المجموعات الحقيقية
    // نبدأ بالبحث في الجذر للحصول على المجموعات
    const rootUrl = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents`;

    let realCollections = [];
    const allDocuments = [];
    const foundCollections = [];
    const errorCollections = [];

    try {
      // محاولة الحصول على قائمة المجموعات من خلال البحث في الجذر
      console.log('🔎 محاولة اكتشاف المجموعات من الجذر...');

      const rootResponse = await fetch(rootUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (rootResponse.ok) {
        const rootData = await rootResponse.json();
        console.log('📄 استجابة الجذر:', rootData);

        // استخراج أسماء المجموعات من المستندات
        if (rootData.documents) {
          const collectionNames = new Set();
          rootData.documents.forEach(doc => {
            const pathParts = doc.name.split('/');
            const collectionName = pathParts[pathParts.length - 2];
            collectionNames.add(collectionName);
          });
          realCollections = Array.from(collectionNames);
          console.log('📁 المجموعات المكتشفة:', realCollections);
        }
      }
    } catch (rootError) {
      console.log('⚠️ لا يمكن الوصول للجذر، سنستخدم طريقة أخرى');
    }

    // إذا لم نجد مجموعات من الجذر، نستخدم المجموعات المعروفة فقط
    if (realCollections.length === 0) {
      console.log('🎯 استخدام المجموعات المعروفة فقط...');
      realCollections = ['courses', 'users', 'test'];
    }

    console.log(`🔍 البحث في ${realCollections.length} مجموعة حقيقية...`);

    // جلب البيانات من المجموعات الحقيقية فقط
    for (const collection of realCollections) {
      try {
        const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/${collection}`;

        console.log(`📂 فحص مجموعة: ${collection}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.documents && data.documents.length > 0) {
            allDocuments.push(...data.documents);
            foundCollections.push({
              name: collection,
              count: data.documents.length
            });
            console.log(`✅ ${collection}: ${data.documents.length} مستند`);
          } else {
            console.log(`📭 ${collection}: فارغة`);
          }
        } else if (response.status === 404) {
          console.log(`❌ ${collection}: غير موجودة`);
        } else {
          errorCollections.push({
            name: collection,
            status: response.status,
            error: response.statusText
          });
          console.log(`❌ ${collection}: خطأ ${response.status}`);
        }
      } catch (collectionError) {
        errorCollections.push({
          name: collection,
          error: collectionError.message
        });
        console.log(`❌ ${collection}: ${collectionError.message}`);
      }
    }

    console.log(`\n📊 ملخص النتائج النهائي:`);
    console.log(`- مجموعات حقيقية مفحوصة: ${realCollections.length}`);
    console.log(`- مجموعات تحتوي على بيانات: ${foundCollections.length}`);
    console.log(`- مجموعات بها أخطاء: ${errorCollections.length}`);
    console.log(`- إجمالي المستندات: ${allDocuments.length}`);

    // إرجاع البيانات الحقيقية فقط
    res.json({
      documents: allDocuments,
      summary: {
        totalSearched: realCollections.length,
        foundCollections: foundCollections.length,
        emptyCollections: 0, // لا نعرض المجموعات الفارغة
        errorCollections: errorCollections.length,
        totalDocuments: allDocuments.length,
        searchType: 'real_collections_only'
      },
      foundCollections: foundCollections,
      realCollections: realCollections,
      errorCollections: errorCollections
    });

  } catch (error) {
    console.error('خطأ في جلب البيانات الحقيقية:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار للحصول على قائمة الكورسات (الأصلي)
app.get('/api/courses', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;

    console.log(`محاولة جلب قائمة الكورسات`);

    // بناء عنوان URL للوصول إلى مجموعة الكورسات في Firestore
    const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/courses`;

    // إرسال طلب للحصول على قائمة الكورسات
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`فشل الاتصال بقاعدة البيانات: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('بيانات الكورسات من Firestore:', JSON.stringify(data, null, 2));

    // في حالة عدم وجود كورسات، نعيد مصفوفة فارغة
    if (!data.documents || data.documents.length === 0) {
      console.log('لا توجد كورسات في قاعدة البيانات');
      return res.json({ documents: [] });
    }

    console.log(`تم العثور على ${data.documents ? data.documents.length : 0} كورس`);
    res.json(data);
  } catch (error) {
    console.error('خطأ في جلب قائمة الكورسات:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لإضافة كورس جديد
app.post('/api/courses', async (req, res) => {
  try {
    const courseData = req.body;
    const projectId = firebaseConfig.projectId;

    console.log(`محاولة إضافة كورس جديد:`, courseData);

    if (!courseData || !courseData.fields) {
      return res.status(400).json({ error: 'بيانات الكورس غير صحيحة' });
    }

    // إنشاء معرف فريد للكورس
    const courseId = 'course_' + Date.now();

    // بناء عنوان URL للوصول إلى مجموعة الكورسات في Firestore
    const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/courses/${courseId}`;

    // إرسال طلب لإضافة الكورس
    const response = await fetch(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(courseData)
    });

    if (!response.ok) {
      throw new Error(`فشل إضافة الكورس: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('تم إضافة الكورس بنجاح:', data);

    res.json({
      success: true,
      message: 'تم إضافة الكورس بنجاح',
      courseId: courseId,
      data: data
    });
  } catch (error) {
    console.error('خطأ في إضافة الكورس:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لاكتشاف جميع قواعد البيانات في المشروع
app.get('/api/discover-databases', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;

    console.log(`🔍 اكتشاف قواعد البيانات في المشروع: ${projectId}`);

    // قائمة قواعد البيانات المحتملة للبحث فيها
    const potentialDatabases = [
      // قاعدة البيانات الافتراضية
      {
        name: '(default)',
        fullName: `projects/${projectId}/databases/(default)`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      // قواعد بيانات محتملة أخرى
      {
        name: 'production',
        fullName: `projects/${projectId}/databases/production`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      {
        name: 'development',
        fullName: `projects/${projectId}/databases/development`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      {
        name: 'staging',
        fullName: `projects/${projectId}/databases/staging`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      {
        name: 'test',
        fullName: `projects/${projectId}/databases/test`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      {
        name: 'backup',
        fullName: `projects/${projectId}/databases/backup`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      {
        name: 'courses-db',
        fullName: `projects/${projectId}/databases/courses-db`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      {
        name: 'users-db',
        fullName: `projects/${projectId}/databases/users-db`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      {
        name: 'analytics',
        fullName: `projects/${projectId}/databases/analytics`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      },
      {
        name: 'logs',
        fullName: `projects/${projectId}/databases/logs`,
        type: 'FIRESTORE_NATIVE',
        locationId: 'us-central1'
      }
    ];

    const databases = [];

    // فحص كل قاعدة بيانات للتأكد من وجودها
    console.log(`🔍 فحص ${potentialDatabases.length} قاعدة بيانات محتملة...`);

    for (const db of potentialDatabases) {
      try {
        // اختبار الوصول لقاعدة البيانات بطرق متعددة
        const testUrls = [
          `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/courses`,
          `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/users`,
          `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/test`,
          `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents`
        ];

        console.log(`🧪 اختبار قاعدة البيانات: ${db.name}`);

        let isAccessible = false;
        let accessMethod = '';

        // جرب كل رابط للتأكد من وجود قاعدة البيانات
        for (const testUrl of testUrls) {
          try {
            const testResponse = await fetch(testUrl, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            // فقط 200 يعني أن قاعدة البيانات موجودة وتحتوي على بيانات
            // 404 يعني أن قاعدة البيانات أو المجموعة غير موجودة
            if (testResponse.status === 200) {
              isAccessible = true;
              accessMethod = testUrl.split('/').pop();
              console.log(`  ✅ ${testUrl} - يحتوي على بيانات`);
              break;
            } else if (testResponse.status === 404) {
              console.log(`  📭 ${testUrl} - غير موجود`);
            } else {
              console.log(`  ❓ ${testUrl} - استجابة غير متوقعة: ${testResponse.status}`);
            }
          } catch (urlError) {
            // تجاهل أخطاء الروابط الفردية
          }
        }

        if (isAccessible) {
          // فحص إضافي: جرب الوصول لجذر قاعدة البيانات للتأكد
          try {
            const rootUrl = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents`;
            const rootResponse = await fetch(rootUrl, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            const dbStatus = rootResponse.status === 200 ? 'has_data' :
                           rootResponse.status === 404 ? 'empty_or_nonexistent' : 'unknown';

            databases.push({
              name: db.name,
              fullName: db.fullName,
              type: db.type,
              locationId: db.locationId,
              status: 'accessible',
              dbStatus: dbStatus,
              accessMethod: accessMethod,
              documentsUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents`,
              collectionsUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents`,
              apiUrl: `http://localhost:3000/api/database/${db.name}/all-data`,
              coursesUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/courses`,
              usersUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/users`,
              testUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/test`,
              allCollectionsUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents`,
              // روابط إضافية للمجموعات المحتملة
              studentsUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/students`,
              teachersUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/teachers`,
              adminsUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/admins`,
              settingsUrl: `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${db.name}/documents/settings`
            });
            console.log(`✅ قاعدة البيانات ${db.name} متاحة (عبر ${accessMethod}) - حالة الجذر: ${dbStatus}`);
          } catch (rootError) {
            console.log(`⚠️ قاعدة البيانات ${db.name} متاحة لكن لا يمكن فحص الجذر`);
          }
        } else {
          console.log(`❌ قاعدة البيانات ${db.name} غير متاحة - لا توجد مجموعات بها بيانات`);
        }
      } catch (error) {
        console.log(`❌ خطأ في اختبار قاعدة البيانات ${db.name}:`, error.message);
      }
    }

    // إضافة روابط إضافية مفيدة
    const additionalLinks = {
      projectConsole: `https://console.firebase.google.com/project/${projectId}`,
      firestoreConsole: `https://console.firebase.google.com/project/${projectId}/firestore`,
      apiDocumentation: 'https://firebase.google.com/docs/firestore/reference/rest',
      localProxy: 'http://localhost:3000',
      allDataEndpoint: 'http://localhost:3000/api/all-data',
      coursesEndpoint: 'http://localhost:3000/api/courses'
    };

    console.log(`📊 تم العثور على ${databases.length} قاعدة بيانات متاحة`);

    res.json({
      projectId: projectId,
      totalDatabases: databases.length,
      databases: databases,
      additionalLinks: additionalLinks,
      timestamp: new Date().toISOString(),
      note: 'تم اكتشاف قواعد البيانات من خلال اختبار الوصول المباشر'
    });

  } catch (error) {
    console.error('خطأ في اكتشاف قواعد البيانات:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لجلب جميع البيانات من قاعدة بيانات محددة
app.get('/api/database/:dbName/all-data', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;
    const dbName = req.params.dbName;

    console.log(`🔍 جلب جميع البيانات من قاعدة البيانات: ${dbName}`);

    // المجموعات المحتملة للبحث فيها
    const potentialCollections = ['courses', 'users', 'test', 'students', 'teachers', 'admins', 'settings', 'data', 'content'];
    const allDocuments = [];
    const foundCollections = [];
    const errorCollections = [];

    for (const collection of potentialCollections) {
      try {
        const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/${dbName}/documents/${collection}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.documents && data.documents.length > 0) {
            allDocuments.push(...data.documents);
            foundCollections.push({
              name: collection,
              count: data.documents.length,
              database: dbName
            });
            console.log(`✅ ${dbName}/${collection}: ${data.documents.length} مستند`);
          }
        }
      } catch (collectionError) {
        errorCollections.push({
          name: collection,
          database: dbName,
          error: collectionError.message
        });
      }
    }

    console.log(`📊 قاعدة البيانات ${dbName}: ${allDocuments.length} مستند في ${foundCollections.length} مجموعة`);

    res.json({
      database: dbName,
      documents: allDocuments,
      summary: {
        totalDocuments: allDocuments.length,
        foundCollections: foundCollections.length,
        errorCollections: errorCollections.length
      },
      foundCollections: foundCollections,
      errorCollections: errorCollections
    });

  } catch (error) {
    console.error(`خطأ في جلب بيانات قاعدة البيانات ${req.params.dbName}:`, error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لاكتشاف المجموعات الحقيقية فقط
app.get('/api/discover-collections', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;

    console.log(`🔍 اكتشاف المجموعات الحقيقية في قاعدة البيانات...`);

    // استخدام طريقة مختلفة: فحص المجموعات المعروفة والتحقق من وجودها
    const potentialCollections = ['courses', 'users', 'test', 'students', 'teachers', 'admins', 'settings'];
    const realCollections = [];

    for (const collection of potentialCollections) {
      try {
        const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/${collection}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.documents && data.documents.length > 0) {
            realCollections.push({
              name: collection,
              count: data.documents.length,
              status: 'has_data'
            });
          } else {
            realCollections.push({
              name: collection,
              count: 0,
              status: 'empty'
            });
          }
        }
      } catch (error) {
        // تجاهل الأخطاء - المجموعة غير موجودة
      }
    }

    console.log('📁 المجموعات المكتشفة:', realCollections);

    res.json({
      realCollections: realCollections,
      totalFound: realCollections.length,
      withData: realCollections.filter(c => c.status === 'has_data').length,
      empty: realCollections.filter(c => c.status === 'empty').length
    });

  } catch (error) {
    console.error('خطأ في اكتشاف المجموعات:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لاكتشاف Firebase Realtime Database مع محاولة طرق مختلفة للمصادقة
app.get('/api/discover-realtime-db', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;

    console.log(`🔍 اكتشاف Firebase Realtime Database للمشروع: ${projectId}`);

    const realtimeDbUrl = `https://${projectId}.firebaseio.com/.json`;

    console.log(`📡 اختبار Realtime Database: ${realtimeDbUrl}`);

    // طرق مختلفة للوصول
    const accessMethods = [
      {
        name: 'بدون مصادقة',
        headers: {
          'Content-Type': 'application/json'
        }
      },
      {
        name: 'مع API Key',
        headers: {
          'Content-Type': 'application/json'
        },
        url: `${realtimeDbUrl}?auth=${firebaseConfig.apiKey}`
      },
      {
        name: 'مع معاملات إضافية',
        headers: {
          'Content-Type': 'application/json'
        },
        url: `${realtimeDbUrl}?print=pretty`
      },
      {
        name: 'مع shallow query',
        headers: {
          'Content-Type': 'application/json'
        },
        url: `${realtimeDbUrl}?shallow=true`
      }
    ];

    let successfulMethod = null;
    let responseData = null;

    // جرب كل طريقة
    for (const method of accessMethods) {
      try {
        console.log(`🧪 جرب طريقة: ${method.name}`);

        const testUrl = method.url || realtimeDbUrl;
        const response = await fetch(testUrl, {
          method: 'GET',
          headers: method.headers
        });

        if (response.ok) {
          const data = await response.json();
          successfulMethod = method.name;
          responseData = data;
          console.log(`✅ نجحت طريقة: ${method.name}`);
          break;
        } else {
          console.log(`❌ فشلت طريقة: ${method.name} - ${response.status}`);
        }
      } catch (methodError) {
        console.log(`❌ خطأ في طريقة: ${method.name} - ${methodError.message}`);
      }
    }

    // إعداد النتيجة
    const realtimeDbInfo = {
      projectId: projectId,
      url: `https://${projectId}.firebaseio.com`,
      apiUrl: realtimeDbUrl,
      accessible: successfulMethod !== null,
      successfulMethod: successfulMethod,
      data: responseData,
      dataSize: responseData ? JSON.stringify(responseData).length : 0,
      hasData: responseData && Object.keys(responseData).length > 0,
      error: successfulMethod ? null : 'جميع طرق الوصول فشلت',
      testedMethods: accessMethods.length
    };

    if (successfulMethod) {
      console.log(`✅ Realtime Database متاح عبر: ${successfulMethod}`);
    } else {
      console.log(`❌ فشل الوصول لـ Realtime Database بجميع الطرق`);
    }

    // إضافة روابط مفيدة مع طرق مختلفة
    realtimeDbInfo.links = {
      root: `https://${projectId}.firebaseio.com/.json`,
      rootWithAuth: `https://${projectId}.firebaseio.com/.json?auth=${firebaseConfig.apiKey}`,
      rootShallow: `https://${projectId}.firebaseio.com/.json?shallow=true`,
      courses: `https://${projectId}.firebaseio.com/courses.json`,
      coursesWithAuth: `https://${projectId}.firebaseio.com/courses.json?auth=${firebaseConfig.apiKey}`,
      users: `https://${projectId}.firebaseio.com/users.json`,
      usersWithAuth: `https://${projectId}.firebaseio.com/users.json?auth=${firebaseConfig.apiKey}`,
      test: `https://${projectId}.firebaseio.com/test.json`,
      console: `https://console.firebase.google.com/project/${projectId}/database`,
      rules: `https://${projectId}.firebaseio.com/.settings/rules.json`
    };

    res.json(realtimeDbInfo);

  } catch (error) {
    console.error('خطأ في اكتشاف Realtime Database:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لجلب بيانات من Realtime Database
app.get('/api/realtime-db/:path?', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;
    const path = req.params.path || '';

    console.log(`🔍 جلب بيانات من Realtime Database: ${path}`);

    const url = `https://${projectId}.firebaseio.com/${path}.json`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      res.json({
        path: path,
        url: url,
        data: data,
        dataSize: JSON.stringify(data).length,
        hasData: data !== null
      });
    } else {
      res.status(response.status).json({
        error: `HTTP ${response.status}`,
        path: path,
        url: url
      });
    }

  } catch (error) {
    console.error('خطأ في جلب بيانات Realtime Database:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لاختبار الوصول لـ Realtime Database بعد تغيير القواعد
app.get('/api/test-realtime-access', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;

    console.log(`🧪 اختبار الوصول لـ Realtime Database بعد تغيير القواعد...`);

    const testUrls = [
      `https://${projectId}.firebaseio.com/.json`,
      `https://${projectId}.firebaseio.com/courses.json`,
      `https://${projectId}.firebaseio.com/users.json`,
      `https://${projectId}.firebaseio.com/test.json`
    ];

    const results = [];

    for (const url of testUrls) {
      try {
        console.log(`📡 اختبار: ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        const result = {
          url: url,
          status: response.status,
          accessible: response.ok,
          data: null,
          error: null
        };

        if (response.ok) {
          const data = await response.json();
          result.data = data;
          result.hasData = data !== null && data !== undefined;
          result.dataSize = JSON.stringify(data).length;
          console.log(`✅ ${url} - يعمل! حجم البيانات: ${result.dataSize}`);
        } else {
          result.error = `HTTP ${response.status}`;
          console.log(`❌ ${url} - خطأ: ${response.status}`);
        }

        results.push(result);

      } catch (urlError) {
        results.push({
          url: url,
          status: 'error',
          accessible: false,
          error: urlError.message
        });
        console.log(`❌ ${url} - خطأ: ${urlError.message}`);
      }
    }

    const summary = {
      totalTested: results.length,
      accessible: results.filter(r => r.accessible).length,
      withData: results.filter(r => r.hasData).length,
      errors: results.filter(r => !r.accessible).length
    };

    console.log(`📊 ملخص الاختبار: ${summary.accessible}/${summary.totalTested} روابط تعمل`);

    res.json({
      projectId: projectId,
      testTime: new Date().toISOString(),
      summary: summary,
      results: results,
      note: 'اختبار الوصول بعد تغيير قواعد الأمان'
    });

  } catch (error) {
    console.error('خطأ في اختبار Realtime Database:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لحذف مجموعة كاملة من Firestore
app.delete('/api/delete-collection/:collectionName', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;
    const collectionName = req.params.collectionName;

    console.log(`🗑️ طلب حذف مجموعة: ${collectionName}`);

    // أولاً: جلب جميع المستندات في المجموعة
    const listUrl = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/${collectionName}`;

    console.log(`📋 جلب قائمة المستندات من: ${listUrl}`);

    const listResponse = await fetch(listUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!listResponse.ok) {
      if (listResponse.status === 404) {
        return res.json({
          success: false,
          error: `المجموعة "${collectionName}" غير موجودة`,
          deletedCount: 0
        });
      }
      throw new Error(`فشل في جلب قائمة المستندات: ${listResponse.status} ${listResponse.statusText}`);
    }

    const listData = await listResponse.json();
    const documents = listData.documents || [];

    if (documents.length === 0) {
      return res.json({
        success: true,
        message: `المجموعة "${collectionName}" فارغة بالفعل`,
        deletedCount: 0
      });
    }

    console.log(`📄 تم العثور على ${documents.length} مستند للحذف`);

    // ثانياً: حذف كل مستند
    const deleteResults = [];
    let deletedCount = 0;
    let errorCount = 0;

    for (const doc of documents) {
      try {
        console.log(`🗑️ حذف مستند: ${doc.name}`);

        // بناء URL كامل للحذف
        const deleteUrl = `https://firestore.googleapis.com/v1/${doc.name}`;

        const deleteResponse = await fetch(deleteUrl, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (deleteResponse.ok) {
          deletedCount++;
          deleteResults.push({
            document: doc.name.split('/').pop(),
            status: 'deleted',
            success: true
          });
          console.log(`✅ تم حذف: ${doc.name.split('/').pop()}`);
        } else {
          errorCount++;
          deleteResults.push({
            document: doc.name.split('/').pop(),
            status: 'error',
            success: false,
            error: `HTTP ${deleteResponse.status}`
          });
          console.log(`❌ فشل حذف: ${doc.name.split('/').pop()} - ${deleteResponse.status}`);
        }
      } catch (docError) {
        errorCount++;
        deleteResults.push({
          document: doc.name.split('/').pop(),
          status: 'error',
          success: false,
          error: docError.message
        });
        console.log(`❌ خطأ في حذف: ${doc.name.split('/').pop()} - ${docError.message}`);
      }
    }

    console.log(`📊 نتائج الحذف: ${deletedCount} نجح، ${errorCount} فشل`);

    // إرجاع النتيجة
    res.json({
      success: deletedCount > 0,
      collectionName: collectionName,
      totalDocuments: documents.length,
      deletedCount: deletedCount,
      errorCount: errorCount,
      results: deleteResults,
      message: deletedCount === documents.length ?
        `تم حذف جميع المستندات (${deletedCount}) من مجموعة "${collectionName}" بنجاح` :
        `تم حذف ${deletedCount} من ${documents.length} مستند. فشل في حذف ${errorCount} مستند.`
    });

  } catch (error) {
    console.error(`خطأ في حذف مجموعة ${req.params.collectionName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      collectionName: req.params.collectionName
    });
  }
});

// مسار لحذف مستند واحد من Firestore
app.delete('/api/delete-document/:collectionName/:documentId', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;
    const collectionName = req.params.collectionName;
    const documentId = req.params.documentId;

    console.log(`🗑️ طلب حذف مستند: ${collectionName}/${documentId}`);

    const deleteUrl = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/${collectionName}/${documentId}`;

    const deleteResponse = await fetch(deleteUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (deleteResponse.ok) {
      console.log(`✅ تم حذف المستند: ${collectionName}/${documentId}`);
      res.json({
        success: true,
        message: `تم حذف المستند "${documentId}" من مجموعة "${collectionName}" بنجاح`,
        collectionName: collectionName,
        documentId: documentId
      });
    } else {
      throw new Error(`فشل في حذف المستند: ${deleteResponse.status} ${deleteResponse.statusText}`);
    }

  } catch (error) {
    console.error(`خطأ في حذف المستند ${req.params.collectionName}/${req.params.documentId}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      collectionName: req.params.collectionName,
      documentId: req.params.documentId
    });
  }
});

// مسار لاختبار طرق مختلفة للوصول لـ Realtime Database
app.get('/api/test-realtime-methods', async (req, res) => {
  try {
    const projectId = firebaseConfig.projectId;

    console.log(`🧪 اختبار طرق مختلفة للوصول لـ Realtime Database`);

    const testMethods = [
      {
        name: 'بدون مصادقة',
        url: `https://${projectId}.firebaseio.com/.json`,
        description: 'الطريقة الأساسية بدون أي مصادقة'
      },
      {
        name: 'مع API Key كمعامل auth',
        url: `https://${projectId}.firebaseio.com/.json?auth=${firebaseConfig.apiKey}`,
        description: 'استخدام API Key كمعامل auth في URL'
      },
      {
        name: 'مع API Key كمعامل key',
        url: `https://${projectId}.firebaseio.com/.json?key=${firebaseConfig.apiKey}`,
        description: 'استخدام API Key كمعامل key في URL'
      },
      {
        name: 'مع shallow query',
        url: `https://${projectId}.firebaseio.com/.json?shallow=true`,
        description: 'جلب أسماء المفاتيح فقط بدون القيم'
      },
      {
        name: 'مع print=pretty',
        url: `https://${projectId}.firebaseio.com/.json?print=pretty`,
        description: 'تنسيق JSON بشكل جميل'
      },
      {
        name: 'مع orderBy',
        url: `https://${projectId}.firebaseio.com/.json?orderBy="$key"`,
        description: 'ترتيب النتائج حسب المفتاح'
      },
      {
        name: 'مع limitToFirst',
        url: `https://${projectId}.firebaseio.com/.json?limitToFirst=1`,
        description: 'جلب أول عنصر فقط'
      },
      {
        name: 'مع API Key في Header',
        url: `https://${projectId}.firebaseio.com/.json`,
        headers: {
          'Authorization': `Bearer ${firebaseConfig.apiKey}`,
          'Content-Type': 'application/json'
        },
        description: 'استخدام API Key في Authorization header'
      },
      {
        name: 'مع X-API-Key Header',
        url: `https://${projectId}.firebaseio.com/.json`,
        headers: {
          'X-API-Key': firebaseConfig.apiKey,
          'Content-Type': 'application/json'
        },
        description: 'استخدام API Key في X-API-Key header'
      }
    ];

    const results = [];

    for (const method of testMethods) {
      try {
        console.log(`🔍 اختبار: ${method.name}`);

        const headers = method.headers || {
          'Content-Type': 'application/json'
        };

        const response = await fetch(method.url, {
          method: 'GET',
          headers: headers
        });

        const result = {
          name: method.name,
          description: method.description,
          url: method.url,
          status: response.status,
          success: response.ok,
          data: null,
          error: null,
          dataSize: 0
        };

        if (response.ok) {
          try {
            const data = await response.json();
            result.data = data;
            result.dataSize = JSON.stringify(data).length;
            result.hasData = data && Object.keys(data).length > 0;
            console.log(`✅ ${method.name} - نجح! حجم البيانات: ${result.dataSize}`);
          } catch (jsonError) {
            result.error = 'فشل في تحليل JSON';
            console.log(`⚠️ ${method.name} - نجح لكن فشل تحليل JSON`);
          }
        } else {
          result.error = `HTTP ${response.status}`;
          console.log(`❌ ${method.name} - فشل: ${response.status}`);
        }

        results.push(result);

      } catch (methodError) {
        results.push({
          name: method.name,
          description: method.description,
          url: method.url,
          status: 'error',
          success: false,
          error: methodError.message,
          data: null,
          dataSize: 0
        });
        console.log(`❌ ${method.name} - خطأ: ${methodError.message}`);
      }
    }

    const summary = {
      totalTested: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      withData: results.filter(r => r.hasData).length
    };

    console.log(`📊 ملخص الاختبار: ${summary.successful}/${summary.totalTested} طريقة نجحت`);

    res.json({
      projectId: projectId,
      testTime: new Date().toISOString(),
      summary: summary,
      results: results,
      note: 'اختبار طرق مختلفة للوصول لـ Realtime Database بدون تغيير الإعدادات'
    });

  } catch (error) {
    console.error('خطأ في اختبار طرق Realtime Database:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لاختبار Firebase Storage
app.get('/api/test-firebase-storage', async (req, res) => {
  try {
    const storageBucket = firebaseConfig.storageBucket;

    console.log(`🗂️ اختبار Firebase Storage: ${storageBucket}`);

    const storageUrls = [
      `https://firebasestorage.googleapis.com/v0/b/${storageBucket}/o/`,
      `https://firebasestorage.googleapis.com/v0/b/${storageBucket}/o?alt=media`,
      `https://storage.googleapis.com/storage/v1/b/${storageBucket}/o`
    ];

    const results = [];

    for (const url of storageUrls) {
      try {
        console.log(`📡 اختبار: ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        const result = {
          url: url,
          status: response.status,
          success: response.ok,
          data: null,
          error: null
        };

        if (response.ok) {
          try {
            const data = await response.json();
            result.data = data;
            result.dataSize = JSON.stringify(data).length;
            console.log(`✅ ${url} - يعمل! حجم البيانات: ${result.dataSize}`);
          } catch (jsonError) {
            result.error = 'فشل في تحليل JSON';
            console.log(`⚠️ ${url} - نجح لكن فشل تحليل JSON`);
          }
        } else {
          result.error = `HTTP ${response.status}`;
          console.log(`❌ ${url} - فشل: ${response.status}`);
        }

        results.push(result);

      } catch (urlError) {
        results.push({
          url: url,
          status: 'error',
          success: false,
          error: urlError.message
        });
        console.log(`❌ ${url} - خطأ: ${urlError.message}`);
      }
    }

    const summary = {
      totalTested: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    };

    console.log(`📊 ملخص اختبار Storage: ${summary.successful}/${summary.totalTested} روابط تعمل`);

    res.json({
      storageBucket: storageBucket,
      testTime: new Date().toISOString(),
      summary: summary,
      results: results,
      storageLinks: {
        bucket: `https://firebasestorage.googleapis.com/v0/b/${storageBucket}/o/`,
        console: `https://console.firebase.google.com/project/e-teaching-8cba6/storage`,
        rules: `https://console.firebase.google.com/project/e-teaching-8cba6/storage/rules`
      }
    });

  } catch (error) {
    console.error('خطأ في اختبار Firebase Storage:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار لاختبار Firebase Authentication
app.get('/api/test-firebase-auth', async (req, res) => {
  try {
    const apiKey = firebaseConfig.apiKey;

    console.log(`🔐 اختبار Firebase Authentication`);

    const authUrls = [
      `https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=${apiKey}`,
      `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${apiKey}`,
      `https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=${apiKey}`,
      `https://identitytoolkit.googleapis.com/v1/accounts:createAuthUri?key=${apiKey}`
    ];

    const results = [];

    for (const url of authUrls) {
      try {
        console.log(`📡 اختبار: ${url}`);

        // اختبار بطلب POST فارغ
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({})
        });

        const result = {
          url: url.split('?')[0], // إخفاء API Key
          endpoint: url.split('/').pop().split('?')[0],
          status: response.status,
          success: response.ok,
          data: null,
          error: null
        };

        if (response.ok) {
          try {
            const data = await response.json();
            result.data = data;
            console.log(`✅ ${result.endpoint} - يعمل!`);
          } catch (jsonError) {
            result.error = 'فشل في تحليل JSON';
            console.log(`⚠️ ${result.endpoint} - نجح لكن فشل تحليل JSON`);
          }
        } else {
          const errorText = await response.text();
          result.error = `HTTP ${response.status}`;
          console.log(`❌ ${result.endpoint} - فشل: ${response.status}`);
        }

        results.push(result);

      } catch (urlError) {
        results.push({
          url: url.split('?')[0],
          endpoint: url.split('/').pop().split('?')[0],
          status: 'error',
          success: false,
          error: urlError.message
        });
        console.log(`❌ ${url.split('/').pop().split('?')[0]} - خطأ: ${urlError.message}`);
      }
    }

    const summary = {
      totalTested: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    };

    console.log(`📊 ملخص اختبار Auth: ${summary.successful}/${summary.totalTested} endpoints تعمل`);

    res.json({
      apiKey: apiKey.substring(0, 10) + '...',
      testTime: new Date().toISOString(),
      summary: summary,
      results: results,
      authLinks: {
        console: `https://console.firebase.google.com/project/e-teaching-8cba6/authentication`,
        users: `https://console.firebase.google.com/project/e-teaching-8cba6/authentication/users`,
        settings: `https://console.firebase.google.com/project/e-teaching-8cba6/authentication/settings`
      }
    });

  } catch (error) {
    console.error('خطأ في اختبار Firebase Authentication:', error);
    res.status(500).json({ error: error.message });
  }
});

// مسار شامل لاختبار جميع خدمات Firebase
app.get('/api/test-all-firebase-services', async (req, res) => {
  try {
    console.log(`🚀 اختبار جميع خدمات Firebase...`);

    const results = {
      firestore: null,
      realtimeDb: null,
      storage: null,
      auth: null,
      summary: {
        totalServices: 4,
        working: 0,
        partiallyWorking: 0,
        notWorking: 0
      }
    };

    // اختبار Firestore
    try {
      const firestoreResponse = await fetch(`http://localhost:3000/api/discover-databases`);
      if (firestoreResponse.ok) {
        results.firestore = await firestoreResponse.json();
        results.firestore.status = 'working';
        results.summary.working++;
      }
    } catch (error) {
      results.firestore = { status: 'error', error: error.message };
      results.summary.notWorking++;
    }

    // اختبار Realtime Database
    try {
      const realtimeResponse = await fetch(`http://localhost:3000/api/discover-realtime-db`);
      if (realtimeResponse.ok) {
        results.realtimeDb = await realtimeResponse.json();
        results.realtimeDb.status = results.realtimeDb.accessible ? 'working' : 'needs_auth';
        if (results.realtimeDb.accessible) {
          results.summary.working++;
        } else {
          results.summary.partiallyWorking++;
        }
      }
    } catch (error) {
      results.realtimeDb = { status: 'error', error: error.message };
      results.summary.notWorking++;
    }

    // اختبار Storage
    try {
      const storageResponse = await fetch(`http://localhost:3000/api/test-firebase-storage`);
      if (storageResponse.ok) {
        results.storage = await storageResponse.json();
        results.storage.status = results.storage.summary.successful > 0 ? 'working' : 'not_working';
        if (results.storage.summary.successful > 0) {
          results.summary.working++;
        } else {
          results.summary.notWorking++;
        }
      }
    } catch (error) {
      results.storage = { status: 'error', error: error.message };
      results.summary.notWorking++;
    }

    // اختبار Authentication
    try {
      const authResponse = await fetch(`http://localhost:3000/api/test-firebase-auth`);
      if (authResponse.ok) {
        results.auth = await authResponse.json();
        results.auth.status = results.auth.summary.successful > 0 ? 'working' : 'not_working';
        if (results.auth.summary.successful > 0) {
          results.summary.working++;
        } else {
          results.summary.notWorking++;
        }
      }
    } catch (error) {
      results.auth = { status: 'error', error: error.message };
      results.summary.notWorking++;
    }

    console.log(`📊 ملخص جميع الخدمات: ${results.summary.working} تعمل، ${results.summary.partiallyWorking} تحتاج مصادقة، ${results.summary.notWorking} لا تعمل`);

    res.json({
      projectId: firebaseConfig.projectId,
      testTime: new Date().toISOString(),
      results: results,
      allLinks: {
        firestore: `https://firestore.googleapis.com/v1/projects/${firebaseConfig.projectId}/databases/(default)/documents`,
        realtimeDb: firebaseConfig.databaseURL,
        storage: `https://firebasestorage.googleapis.com/v0/b/${firebaseConfig.storageBucket}/o/`,
        console: `https://console.firebase.google.com/project/${firebaseConfig.projectId}`
      }
    });

  } catch (error) {
    console.error('خطأ في اختبار جميع خدمات Firebase:', error);
    res.status(500).json({ error: error.message });
  }
});

// تشغيل الخادم
app.listen(PORT, () => {
  console.log(`الخادم الوسيط يعمل على المنفذ ${PORT}`);
  console.log(`يمكنك الوصول إليه على http://localhost:${PORT}`);
});
