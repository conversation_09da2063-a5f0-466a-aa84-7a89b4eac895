<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الكورسات</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .courses-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .courses-list {
            margin-top: 20px;
        }

        .course-item {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }

        .course-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .course-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .course-description {
            color: #666;
            margin-bottom: 10px;
        }

        .course-instructor {
            color: #4285f4;
            font-weight: bold;
        }

        .course-details {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            color: #777;
            font-size: 14px;
        }

        .loading-message {
            text-align: center;
            padding: 20px;
            color: #4285f4;
        }

        .error-message {
            text-align: center;
            padding: 20px;
            color: #d93025;
            background-color: #fce8e6;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #d93025;
        }

        .error-details {
            margin-top: 15px;
            text-align: right;
            font-size: 14px;
        }

        .error-details p {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .error-details ul {
            padding-right: 20px;
        }

        .error-details li {
            margin-bottom: 5px;
        }

        .success-message {
            text-align: center;
            padding: 15px;
            color: #0f9d58;
            background-color: #e6f4ea;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #0f9d58;
        }

        .buttons-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .back-btn {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .back-btn:hover {
            background-color: #3367d6;
        }

        .add-course-btn {
            background-color: #0f9d58;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .add-course-btn:hover {
            background-color: #0b8043;
        }

        .no-courses {
            text-align: center;
            padding: 30px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="courses-container">
            <h2>قائمة الكورسات المتاحة</h2>
            <div class="buttons-container">
                <button id="back-btn" class="back-btn">العودة إلى الصفحة الرئيسية</button>
                <button id="add-course-btn" class="add-course-btn">إضافة كورس جديد</button>
            </div>

            <div id="loading-message" class="loading-message">جاري تحميل الكورسات...</div>
            <div id="error-message" class="error-message" style="display: none;"></div>

            <div id="courses-list" class="courses-list"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>

    <!-- تكوين Firebase والتطبيق -->
    <script src="firebase-config.js"></script>
    <script src="firebase-db.js"></script>
    <script src="courses.js"></script>
</body>
</html>
