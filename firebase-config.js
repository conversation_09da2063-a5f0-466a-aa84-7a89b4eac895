// تكوين Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDiCkboWAjaRQrBrHCcI1biQr8GfjgsZn0",
  authDomain: "e-teaching-8cba6.firebaseapp.com",
  databaseURL: "https://e-teaching-8cba6.firebaseio.com",
  projectId: "e-teaching-8cba6",
  storageBucket: "e-teaching-8cba6.appspot.com",
  messagingSenderId: "781763310298",
  appId: "1:781763310298:web:d79f584e3c0cdfd3f6abe3",
  measurementId: "G-L411M6CLDW"
};

// تهيئة Firebase
firebase.initializeApp(firebaseConfig);

// تجاوز مشكلة المجال غير المصرح به (للأغراض التعليمية فقط)
// هذا الكود يحاول تجاوز بعض قيود الأمان للسماح بالوصول من المجال المحلي
// لا ينصح باستخدام هذا في بيئة الإنتاج
const auth = firebase.auth();
auth.useDeviceLanguage();

// تكوين إعدادات تسجيل الدخول بالبريد الإلكتروني
const actionCodeSettings = {
  // عنوان URL الذي سيتم توجيه المستخدم إليه بعد النقر على الرابط في البريد الإلكتروني
  url: window.location.origin + window.location.pathname,
  // يجب أن يكون هذا صحيحًا للتعامل مع الرابط في التطبيق
  handleCodeInApp: true
};

// محاولة تعيين مجال مخصص للمصادقة
try {
  auth.setPersistence(firebase.auth.Auth.Persistence.LOCAL)
    .then(() => {
      console.log("تم تعيين المثابرة بنجاح");
    })
    .catch((error) => {
      console.error("خطأ في تعيين المثابرة:", error);
    });
} catch (e) {
  console.error("خطأ في تعيين المثابرة:", e);
}

// تعريف دالة مساعدة لإنشاء حساب باستخدام REST API بدلاً من SDK
window.createUserWithEmailAndPassword = function(email, password) {
  return new Promise((resolve, reject) => {
    const apiKey = firebaseConfig.apiKey;
    // محاولة تسجيل الدخول مباشرة بدلاً من إنشاء حساب جديد
    const url = `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${apiKey}`;

    console.log("إرسال طلب تسجيل الدخول إلى:", url);

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        password: password,
        returnSecureToken: true
      })
    })
    .then(response => {
      console.log("استجابة الخادم:", response.status, response.statusText);
      return response.json();
    })
    .then(data => {
      console.log("بيانات الاستجابة:", data);
      if (data.error) {
        console.error("خطأ في إنشاء الحساب:", data.error);
        reject(data.error);
      } else {
        // تسجيل الدخول تلقائيًا بعد إنشاء الحساب
        console.log("تم إنشاء الحساب بنجاح، محاولة تسجيل الدخول");
        auth.signInWithEmailAndPassword(email, password)
          .then(userCredential => {
            console.log("تم تسجيل الدخول بنجاح:", userCredential);
            resolve(userCredential);
          })
          .catch(error => {
            console.error("خطأ في تسجيل الدخول بعد إنشاء الحساب:", error);
            reject(error);
          });
      }
    })
    .catch(error => {
      console.error("خطأ في طلب إنشاء الحساب:", error);
      reject(error);
    });
  });
};
