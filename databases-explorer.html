<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستكشف قواعد البيانات - Firebase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }

        .discover-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .discover-btn:hover {
            background: #3367d6;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4285f4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .project-info {
            padding: 20px;
            background: #e8f5e8;
            text-align: center;
            border-bottom: 1px solid #c8e6c9;
        }

        .project-id {
            font-size: 1.5em;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .timestamp {
            color: #666;
            font-size: 0.9em;
        }

        .databases-container {
            padding: 30px;
        }

        .database-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid #e9ecef;
        }

        .database-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #4285f4;
        }

        .database-header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 20px;
        }

        .database-name {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .database-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .database-type {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .database-location {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .database-body {
            padding: 25px;
        }

        .links-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .link-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-right: 4px solid #4285f4;
            transition: all 0.3s ease;
        }

        .link-card:hover {
            background: #e3f2fd;
            transform: translateX(-5px);
        }

        .link-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .link-url {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            word-break: break-all;
            font-size: 0.9em;
            color: #666;
            margin-bottom: 10px;
        }

        .link-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #3367d6;
        }

        .action-btn.copy {
            background: #34a853;
        }

        .action-btn.copy:hover {
            background: #2e7d32;
        }

        .action-btn.test {
            background: #ea4335;
        }

        .action-btn.test:hover {
            background: #d33b2c;
        }

        .data-preview {
            margin-top: 20px;
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .preview-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .preview-content {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #d32f2f;
            background: #ffebee;
            margin: 20px;
            border-radius: 8px;
        }

        .no-databases {
            text-align: center;
            padding: 50px;
            color: #666;
            font-style: italic;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .copy-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .copy-notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ مستكشف قواعد البيانات</h1>
            <p>اكتشاف وعرض جميع قواعد البيانات في مشروع Firebase</p>
        </div>

        <div class="controls">
            <button class="discover-btn" onclick="discoverDatabases()">
                🔍 اكتشاف قواعد البيانات
            </button>
            <button class="discover-btn" onclick="refreshAll()">
                🔄 تحديث الكل
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            جاري اكتشاف قواعد البيانات...
        </div>

        <div id="project-info" class="project-info" style="display: none;">
            <div class="project-id" id="project-id"></div>
            <div class="timestamp" id="timestamp"></div>
        </div>

        <div id="databases-container" class="databases-container"></div>

        <div id="error-container" style="display: none;"></div>
    </div>

    <div id="copy-notification" class="copy-notification">
        تم نسخ الرابط! 📋
    </div>

    <script>
        let discoveredDatabases = [];

        // اكتشاف قواعد البيانات عند فتح الصفحة
        window.onload = function() {
            discoverDatabases();
        };

        async function discoverDatabases() {
            const loading = document.getElementById('loading');
            const databasesContainer = document.getElementById('databases-container');
            const errorContainer = document.getElementById('error-container');
            const projectInfo = document.getElementById('project-info');

            // إظهار التحميل
            loading.style.display = 'block';
            databasesContainer.innerHTML = '';
            errorContainer.style.display = 'none';
            projectInfo.style.display = 'none';

            try {
                console.log('🔍 بدء اكتشاف قواعد البيانات...');

                const response = await fetch('http://localhost:3000/api/discover-databases');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                discoveredDatabases = data.databases;

                console.log('📊 تم اكتشاف قواعد البيانات:', data);

                // إخفاء التحميل
                loading.style.display = 'none';

                // عرض معلومات المشروع
                displayProjectInfo(data);

                // عرض قواعد البيانات
                displayDatabases(data.databases);

            } catch (error) {
                loading.style.display = 'none';
                showError(error.message);
                console.error('خطأ في اكتشاف قواعد البيانات:', error);
            }
        }

        function displayProjectInfo(data) {
            const projectInfo = document.getElementById('project-info');
            const projectId = document.getElementById('project-id');
            const timestamp = document.getElementById('timestamp');

            projectId.textContent = `مشروع: ${data.projectId}`;
            timestamp.textContent = `آخر تحديث: ${new Date(data.timestamp).toLocaleString('ar-EG')}`;

            projectInfo.style.display = 'block';
        }

        function displayDatabases(databases) {
            const container = document.getElementById('databases-container');

            if (!databases || databases.length === 0) {
                container.innerHTML = `
                    <div class="no-databases">
                        <h3>📭 لا توجد قواعد بيانات</h3>
                        <p>لم يتم العثور على أي قواعد بيانات في هذا المشروع</p>
                    </div>
                `;
                return;
            }

            // إحصائيات سريعة
            const statsHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">${databases.length}</div>
                        <div class="stat-label">قواعد البيانات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${databases.filter(db => db.type === 'FIRESTORE_NATIVE').length}</div>
                        <div class="stat-label">Firestore</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${new Set(databases.map(db => db.locationId)).size}</div>
                        <div class="stat-label">المواقع</div>
                    </div>
                </div>
            `;

            // عرض كل قاعدة بيانات
            const databasesHTML = databases.map(db => {
                return `
                    <div class="database-card">
                        <div class="database-header">
                            <div class="database-name">🗄️ ${db.name}</div>
                            <div class="database-info">
                                <div class="database-type">${db.type}</div>
                                <div class="database-location">📍 ${db.locationId}</div>
                            </div>
                        </div>
                        <div class="database-body">
                            <div class="links-section">
                                <div class="link-card">
                                    <div class="link-title">🔗 جميع المستندات</div>
                                    <div class="link-url">${db.documentsUrl}</div>
                                    <div class="link-actions">
                                        <button class="action-btn copy" onclick="copyToClipboard('${db.documentsUrl}')">📋 نسخ</button>
                                        <button class="action-btn test" onclick="testDirectLink('${db.documentsUrl}')">🧪 اختبار</button>
                                    </div>
                                </div>
                                <div class="link-card">
                                    <div class="link-title">📚 الكورسات</div>
                                    <div class="link-url">${db.coursesUrl}</div>
                                    <div class="link-actions">
                                        <button class="action-btn copy" onclick="copyToClipboard('${db.coursesUrl}')">📋 نسخ</button>
                                        <button class="action-btn test" onclick="testDirectLink('${db.coursesUrl}')">🧪 اختبار</button>
                                    </div>
                                </div>
                                <div class="link-card">
                                    <div class="link-title">👤 المستخدمون</div>
                                    <div class="link-url">${db.usersUrl}</div>
                                    <div class="link-actions">
                                        <button class="action-btn copy" onclick="copyToClipboard('${db.usersUrl}')">📋 نسخ</button>
                                        <button class="action-btn test" onclick="testDirectLink('${db.usersUrl}')">🧪 اختبار</button>
                                    </div>
                                </div>
                                <div class="link-card">
                                    <div class="link-title">🧪 البيانات التجريبية</div>
                                    <div class="link-url">${db.testUrl}</div>
                                    <div class="link-actions">
                                        <button class="action-btn copy" onclick="copyToClipboard('${db.testUrl}')">📋 نسخ</button>
                                        <button class="action-btn test" onclick="testDirectLink('${db.testUrl}')">🧪 اختبار</button>
                                    </div>
                                </div>
                                ${db.studentsUrl ? `
                                <div class="link-card">
                                    <div class="link-title">🎓 الطلاب</div>
                                    <div class="link-url">${db.studentsUrl}</div>
                                    <div class="link-actions">
                                        <button class="action-btn copy" onclick="copyToClipboard('${db.studentsUrl}')">📋 نسخ</button>
                                        <button class="action-btn test" onclick="testDirectLink('${db.studentsUrl}')">🧪 اختبار</button>
                                    </div>
                                </div>
                                ` : ''}
                                ${db.teachersUrl ? `
                                <div class="link-card">
                                    <div class="link-title">👨‍🏫 المدرسون</div>
                                    <div class="link-url">${db.teachersUrl}</div>
                                    <div class="link-actions">
                                        <button class="action-btn copy" onclick="copyToClipboard('${db.teachersUrl}')">📋 نسخ</button>
                                        <button class="action-btn test" onclick="testDirectLink('${db.teachersUrl}')">🧪 اختبار</button>
                                    </div>
                                </div>
                                ` : ''}
                                <div class="link-card">
                                    <div class="link-title">🚀 API الخادم الوسيط</div>
                                    <div class="link-url">${db.apiUrl}</div>
                                    <div class="link-actions">
                                        <button class="action-btn copy" onclick="copyToClipboard('${db.apiUrl}')">📋 نسخ</button>
                                        <button class="action-btn" onclick="loadDatabaseData('${db.name}')">📊 جلب البيانات</button>
                                    </div>
                                </div>
                            </div>
                            <div id="preview-${db.name}" class="data-preview" style="display: none;">
                                <div class="preview-title">📄 معاينة البيانات:</div>
                                <div id="preview-content-${db.name}" class="preview-content"></div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = statsHTML + databasesHTML;
        }

        async function loadDatabaseData(dbName) {
            const previewDiv = document.getElementById(`preview-${dbName}`);
            const contentDiv = document.getElementById(`preview-content-${dbName}`);

            previewDiv.style.display = 'block';
            contentDiv.textContent = 'جاري تحميل البيانات...';

            try {
                const response = await fetch(`http://localhost:3000/api/database/${dbName}/all-data`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                contentDiv.textContent = JSON.stringify(data, null, 2);

            } catch (error) {
                contentDiv.textContent = `خطأ في تحميل البيانات: ${error.message}`;
            }
        }

        async function testDirectLink(url) {
            try {
                console.log('🧪 اختبار الرابط المباشر:', url);

                const response = await fetch(url);

                if (response.ok) {
                    const data = await response.json();
                    alert(`✅ الرابط يعمل!\nعدد المستندات: ${data.documents ? data.documents.length : 0}`);
                } else {
                    alert(`❌ الرابط لا يعمل!\nكود الخطأ: ${response.status}`);
                }
            } catch (error) {
                alert(`❌ خطأ في الاتصال: ${error.message}`);
            }
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showCopyNotification();
            }).catch(err => {
                console.error('خطأ في النسخ:', err);
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showCopyNotification();
            });
        }

        function showCopyNotification() {
            const notification = document.getElementById('copy-notification');
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 2000);
        }

        function refreshAll() {
            discoverDatabases();
        }

        function showError(message) {
            const errorContainer = document.getElementById('error-container');
            errorContainer.innerHTML = `
                <div class="error">
                    <h3>❌ حدث خطأ في اكتشاف قواعد البيانات</h3>
                    <p>${message}</p>
                    <button class="discover-btn" onclick="discoverDatabases()" style="margin-top: 15px;">
                        🔄 إعادة المحاولة
                    </button>
                </div>
            `;
            errorContainer.style.display = 'block';
        }
    </script>
</body>
</html>
