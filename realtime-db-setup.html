<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد Realtime Database - إلغاء المصادقة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .step {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: #f8f9fa;
        }

        .step.current {
            border-color: #ff6b6b;
            background: #fff5f5;
        }

        .step-number {
            background: #ff6b6b;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 15px;
        }

        .step-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .step-content {
            color: #666;
            line-height: 1.6;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #4285f4;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .copy-btn:hover {
            background: #3367d6;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .warning strong {
            color: #d63031;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .link-btn {
            display: inline-block;
            background: #4285f4;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }

        .link-btn:hover {
            background: #3367d6;
            transform: translateY(-2px);
        }

        .link-btn.danger {
            background: #ea4335;
        }

        .link-btn.danger:hover {
            background: #d33b2c;
        }

        .test-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }

        .test-btn {
            background: #34a853;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            background: #2e7d32;
            transform: translateY(-2px);
        }

        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .screenshot {
            max-width: 100%;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 إعداد Realtime Database</h1>
            <p>دليل خطوة بخطوة لإلغاء المصادقة من Firebase Realtime Database</p>
        </div>

        <div class="content">
            <div class="warning">
                <strong>⚠️ تحذير أمني مهم:</strong> 
                تغيير قواعد الأمان لتصبح عامة يجعل قاعدة البيانات مفتوحة للجميع. 
                استخدم هذا فقط للاختبار وليس في الإنتاج!
            </div>

            <div class="step current">
                <div class="step-number">1</div>
                <div class="step-title">فتح Firebase Console</div>
                <div class="step-content">
                    <p>اذهب إلى وحة تحكم Firebase لمشروعك:</p>
                    <a href="https://console.firebase.google.com/project/e-teaching-8cba6/database" 
                       target="_blank" class="link-btn">
                        🌐 فتح Firebase Console
                    </a>
                    <p style="margin-top: 15px;">
                        <strong>ملاحظة:</strong> تأكد من تسجيل الدخول بحساب Google الذي يملك صلاحيات على المشروع.
                    </p>
                </div>
            </div>

            <div class="step">
                <div class="step-number">2</div>
                <div class="step-title">الذهاب إلى Realtime Database</div>
                <div class="step-content">
                    <p>في القائمة الجانبية:</p>
                    <ul style="margin: 15px 0; padding-right: 20px;">
                        <li>انقر على <strong>"Build"</strong> أو <strong>"إنشاء"</strong></li>
                        <li>ثم انقر على <strong>"Realtime Database"</strong></li>
                        <li>إذا لم تجدها، ابحث عن <strong>"Database"</strong> ثم اختر <strong>"Realtime Database"</strong></li>
                    </ul>
                </div>
            </div>

            <div class="step">
                <div class="step-number">3</div>
                <div class="step-title">فتح قواعد الأمان</div>
                <div class="step-content">
                    <p>في صفحة Realtime Database:</p>
                    <ul style="margin: 15px 0; padding-right: 20px;">
                        <li>انقر على تبويب <strong>"Rules"</strong> أو <strong>"القواعد"</strong></li>
                        <li>ستظهر لك قواعد الأمان الحالية</li>
                    </ul>
                </div>
            </div>

            <div class="step">
                <div class="step-number">4</div>
                <div class="step-title">تغيير القواعد</div>
                <div class="step-content">
                    <p><strong>القواعد الحالية (تتطلب مصادقة):</strong></p>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode('current-rules')">نسخ</button>
                        <pre id="current-rules">{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null"
  }
}</pre>
                    </div>

                    <p><strong>القواعد الجديدة (بدون مصادقة):</strong></p>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode('new-rules')">نسخ</button>
                        <pre id="new-rules">{
  "rules": {
    ".read": true,
    ".write": true
  }
}</pre>
                    </div>

                    <div class="warning">
                        <strong>خطوات التغيير:</strong>
                        <ol style="margin: 10px 0; padding-right: 20px;">
                            <li>احذف القواعد الحالية</li>
                            <li>انسخ القواعد الجديدة والصقها</li>
                            <li>انقر على <strong>"Publish"</strong> أو <strong>"نشر"</strong></li>
                            <li>أكد التغيير عند ظهور رسالة التحذير</li>
                        </ol>
                    </div>
                </div>
            </div>

            <div class="step">
                <div class="step-number">5</div>
                <div class="step-title">بديل أكثر أماناً (قراءة فقط)</div>
                <div class="step-content">
                    <p>إذا كنت تريد السماح بالقراءة فقط بدون مصادقة:</p>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode('readonly-rules')">نسخ</button>
                        <pre id="readonly-rules">{
  "rules": {
    ".read": true,
    ".write": "auth != null"
  }
}</pre>
                    </div>
                    <p>هذا يسمح بقراءة البيانات بدون مصادقة، لكن الكتابة تتطلب مصادقة.</p>
                </div>
            </div>

            <div class="test-section">
                <h3>🧪 اختبار الوصول</h3>
                <p>بعد تطبيق التغييرات، اختبر الوصول لقاعدة البيانات:</p>
                <button class="test-btn" onclick="testRealtimeAccess()">
                    اختبار الوصول الآن
                </button>
                <div id="test-result" class="test-result"></div>
            </div>

            <div class="step">
                <div class="step-number">6</div>
                <div class="step-title">الروابط المتاحة بعد التغيير</div>
                <div class="step-content">
                    <p>بعد تطبيق التغييرات، ستتمكن من الوصول لهذه الروابط:</p>
                    
                    <div style="margin: 20px 0;">
                        <h4>🔗 روابط مباشرة:</h4>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode('direct-links')">نسخ الكل</button>
                            <pre id="direct-links">🔥 الجذر الرئيسي:
https://e-teaching-8cba6.firebaseio.com/.json

📚 الكورسات:
https://e-teaching-8cba6.firebaseio.com/courses.json

👤 المستخدمون:
https://e-teaching-8cba6.firebaseio.com/users.json

🧪 البيانات التجريبية:
https://e-teaching-8cba6.firebaseio.com/test.json</pre>
                        </div>
                    </div>

                    <div style="margin: 20px 0;">
                        <h4>🚀 روابط API الخادم الوسيط:</h4>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode('api-links')">نسخ الكل</button>
                            <pre id="api-links">🔍 اكتشاف Realtime Database:
http://localhost:3000/api/discover-realtime-db

🧪 اختبار الوصول:
http://localhost:3000/api/test-realtime-access

📊 جلب بيانات محددة:
http://localhost:3000/api/realtime-db/courses
http://localhost:3000/api/realtime-db/users</pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="success">
                <strong>✅ بعد تطبيق التغييرات:</strong>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>ستتمكن من قراءة وكتابة البيانات بدون مصادقة</li>
                    <li>جميع الروابط أعلاه ستعمل بشكل طبيعي</li>
                    <li>يمكنك استخدام API الخادم الوسيط للوصول للبيانات</li>
                    <li>تذكر إعادة تفعيل الأمان عند الانتهاء من الاختبار</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function copyCode(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                const btn = element.parentElement.querySelector('.copy-btn');
                const originalText = btn.textContent;
                btn.textContent = 'تم النسخ!';
                btn.style.background = '#34a853';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#4285f4';
                }, 2000);
            }).catch(err => {
                console.error('خطأ في النسخ:', err);
                alert('فشل في نسخ النص. يرجى النسخ يدوياً.');
            });
        }

        async function testRealtimeAccess() {
            const resultDiv = document.getElementById('test-result');
            const btn = document.querySelector('.test-btn');
            
            btn.textContent = 'جاري الاختبار...';
            btn.disabled = true;
            resultDiv.style.display = 'none';
            
            try {
                const response = await fetch('http://localhost:3000/api/test-realtime-access');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.summary.accessible > 0) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        <h4>✅ نجح الاختبار!</h4>
                        <p>تم الوصول بنجاح إلى ${data.summary.accessible} من ${data.summary.totalTested} روابط.</p>
                        <p>قواعد الأمان تم تغييرها بنجاح!</p>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <h4>❌ فشل الاختبار</h4>
                        <p>لا يزال الوصول مرفوض. تأكد من تطبيق تغييرات قواعد الأمان في Firebase Console.</p>
                        <p>جميع الروابط (${data.summary.totalTested}) ترجع خطأ مصادقة.</p>
                    `;
                }
                
                resultDiv.style.display = 'block';

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <h4>❌ خطأ في الاختبار</h4>
                    <p>حدث خطأ أثناء الاختبار: ${error.message}</p>
                `;
                resultDiv.style.display = 'block';
            }
            
            btn.textContent = 'اختبار الوصول الآن';
            btn.disabled = false;
        }
    </script>
</body>
</html>
