<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 محاكي اختبار Telegram - أرقام عشوائية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #0088cc 0%, #005580 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #0088cc 0%, #005580 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }

        .test-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #0088cc;
            margin-bottom: 15px;
        }

        .phone-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
        }

        .phone-input:focus {
            outline: none;
            border-color: #0088cc;
        }

        .btn {
            background: #0088cc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #005580;
            transform: translateY(-2px);
        }

        .btn.danger {
            background: #dc3545;
        }

        .btn.danger:hover {
            background: #c82333;
        }

        .result-box {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }

        .result-box.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result-box.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .result-box.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .test-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .test-card.passed {
            border-color: #28a745;
            background: #f8fff9;
        }

        .test-card.failed {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .test-card.pending {
            border-color: #ffc107;
            background: #fffdf5;
        }

        .status-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0088cc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }

        .analysis-section {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .analysis-title {
            color: #1976d2;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .phone-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .phone-example {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .phone-example:hover {
            border-color: #0088cc;
            transform: translateY(-2px);
        }

        .phone-example.invalid {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .phone-example.valid {
            border-color: #28a745;
            background: #f8fff9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 محاكي اختبار Telegram</h1>
            <p>اختبار سلوك الموقع مع أرقام هواتف عشوائية</p>
        </div>

        <div class="content">
            <!-- قسم الاختبار اليدوي -->
            <div class="test-section">
                <div class="test-title">📱 اختبار رقم هاتف:</div>
                <input type="text" class="phone-input" id="phone-input" placeholder="+1234567890" maxlength="15">
                <button class="btn" onclick="testPhoneNumber()">🧪 اختبار الرقم</button>
                <button class="btn" onclick="generateRandomPhone()">🎲 رقم عشوائي</button>
                <div id="test-result" class="result-box"></div>
            </div>

            <!-- أمثلة أرقام للاختبار -->
            <div class="test-section">
                <div class="test-title">📋 أمثلة أرقام للاختبار:</div>
                <div class="phone-examples">
                    <div class="phone-example invalid" onclick="testSpecificPhone('+1234567890')">
                        <strong>+1234567890</strong><br>
                        <small>رقم وهمي</small>
                    </div>
                    <div class="phone-example invalid" onclick="testSpecificPhone('+9999999999')">
                        <strong>+9999999999</strong><br>
                        <small>رقم غير صحيح</small>
                    </div>
                    <div class="phone-example invalid" onclick="testSpecificPhone('+0000000000')">
                        <strong>+0000000000</strong><br>
                        <small>أصفار فقط</small>
                    </div>
                    <div class="phone-example invalid" onclick="testSpecificPhone('+123')">
                        <strong>+123</strong><br>
                        <small>قصير جداً</small>
                    </div>
                    <div class="phone-example invalid" onclick="testSpecificPhone('+12345678901234567890')">
                        <strong>+123...890</strong><br>
                        <small>طويل جداً</small>
                    </div>
                    <div class="phone-example invalid" onclick="testSpecificPhone('1234567890')">
                        <strong>1234567890</strong><br>
                        <small>بدون رمز دولي</small>
                    </div>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="test-section">
                <div class="test-title">📊 نتائج الاختبارات:</div>
                <div class="test-results" id="test-results">
                    <!-- ستملأ بالنتائج -->
                </div>
            </div>

            <!-- تحليل السلوك -->
            <div class="analysis-section">
                <div class="analysis-title">🔍 تحليل سلوك موقع Telegram:</div>
                <div id="behavior-analysis">
                    <p>قم بإجراء بعض الاختبارات لرؤية التحليل...</p>
                </div>
            </div>

            <!-- الكود المحاكي -->
            <div class="test-section">
                <div class="test-title">💻 محاكاة سلوك الموقع:</div>
                <div class="code-block" id="simulation-code">
// محاكاة سلوك my.telegram.org
function simulateTelegramBehavior(phoneNumber) {
    console.log('🔍 اختبار رقم:', phoneNumber);
    
    // التحقق من صحة الرقم
    const validation = validatePhoneNumber(phoneNumber);
    
    if (!validation.isValid) {
        return {
            success: false,
            error: validation.error,
            action: 'رفض الرقم'
        };
    }
    
    // محاكاة إرسال الكود
    const codeResult = simulateCodeSending(phoneNumber);
    
    return {
        success: codeResult.sent,
        message: codeResult.message,
        action: codeResult.action,
        timing: codeResult.timing
    };
}
                </div>
            </div>
        </div>
    </div>

    <script>
        let testCounter = 0;
        let testResults = [];

        function validatePhoneNumber(phone) {
            // إزالة المسافات والرموز الإضافية
            const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
            
            // التحقق من وجود رمز الدولة
            if (!cleanPhone.startsWith('+')) {
                return {
                    isValid: false,
                    error: 'يجب أن يبدأ الرقم برمز الدولة (+)'
                };
            }
            
            // التحقق من الطول
            if (cleanPhone.length < 8) {
                return {
                    isValid: false,
                    error: 'الرقم قصير جداً'
                };
            }
            
            if (cleanPhone.length > 16) {
                return {
                    isValid: false,
                    error: 'الرقم طويل جداً'
                };
            }
            
            // التحقق من الأرقام فقط بعد +
            const numberPart = cleanPhone.substring(1);
            if (!/^\d+$/.test(numberPart)) {
                return {
                    isValid: false,
                    error: 'يجب أن يحتوي على أرقام فقط'
                };
            }
            
            // التحقق من الأرقام الوهمية
            const commonFakeNumbers = [
                '+1234567890',
                '+9999999999',
                '+0000000000',
                '+1111111111',
                '+5555555555'
            ];
            
            if (commonFakeNumbers.includes(cleanPhone)) {
                return {
                    isValid: false,
                    error: 'رقم وهمي معروف'
                };
            }
            
            return {
                isValid: true,
                cleanPhone: cleanPhone
            };
        }

        function simulateCodeSending(phone) {
            const validation = validatePhoneNumber(phone);
            
            if (!validation.isValid) {
                return {
                    sent: false,
                    message: validation.error,
                    action: 'رفض الرقم',
                    timing: 0
                };
            }
            
            // محاكاة التحقق من وجود الرقم في Telegram
            const isRegistered = Math.random() > 0.7; // 30% احتمال أن يكون مسجل
            
            if (!isRegistered) {
                return {
                    sent: false,
                    message: 'هذا الرقم غير مسجل في Telegram',
                    action: 'رفض - غير مسجل',
                    timing: 2000
                };
            }
            
            // محاكاة إرسال الكود
            const sendingTime = Math.random() * 5000 + 1000; // 1-6 ثواني
            
            return {
                sent: true,
                message: 'تم إرسال كود التأكيد عبر Telegram',
                action: 'إرسال كود',
                timing: sendingTime,
                code: Math.floor(10000 + Math.random() * 90000) // كود 5 أرقام
            };
        }

        function testPhoneNumber() {
            const phone = document.getElementById('phone-input').value;
            const resultDiv = document.getElementById('test-result');
            
            if (!phone.trim()) {
                showResult('error', 'يرجى إدخال رقم هاتف');
                return;
            }
            
            // إظهار حالة التحميل
            showResult('warning', '🔄 جاري اختبار الرقم...', true);
            
            // محاكاة التأخير
            setTimeout(() => {
                const result = simulateCodeSending(phone);
                addTestResult(phone, result);
                
                if (result.sent) {
                    showResult('success', 
                        `✅ ${result.message}<br>` +
                        `⏰ وقت الإرسال: ${(result.timing/1000).toFixed(1)} ثانية<br>` +
                        `🔢 الكود المرسل: ${result.code}`
                    );
                } else {
                    showResult('error', `❌ ${result.message}`);
                }
                
                updateBehaviorAnalysis();
            }, 1500);
        }

        function generateRandomPhone() {
            const countryCodes = ['+1', '+44', '+49', '+33', '+39', '+34', '+7', '+86', '+81', '+82'];
            const countryCode = countryCodes[Math.floor(Math.random() * countryCodes.length)];
            const numberLength = Math.floor(Math.random() * 6) + 7; // 7-12 أرقام
            
            let number = countryCode;
            for (let i = 0; i < numberLength; i++) {
                number += Math.floor(Math.random() * 10);
            }
            
            document.getElementById('phone-input').value = number;
        }

        function testSpecificPhone(phone) {
            document.getElementById('phone-input').value = phone;
            testPhoneNumber();
        }

        function showResult(type, message, loading = false) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = `result-box ${type}`;
            resultDiv.innerHTML = loading ? 
                `<div class="loading"></div> ${message}` : 
                message;
            resultDiv.style.display = 'block';
        }

        function addTestResult(phone, result) {
            testCounter++;
            testResults.push({
                id: testCounter,
                phone: phone,
                result: result,
                timestamp: new Date()
            });
            
            updateTestResults();
        }

        function updateTestResults() {
            const container = document.getElementById('test-results');
            
            container.innerHTML = testResults.slice(-6).map(test => `
                <div class="test-card ${test.result.sent ? 'passed' : 'failed'}">
                    <div class="status-icon">${test.result.sent ? '✅' : '❌'}</div>
                    <strong>${test.phone}</strong><br>
                    <small>${test.result.action}</small><br>
                    <small>${test.timestamp.toLocaleTimeString()}</small>
                </div>
            `).join('');
        }

        function updateBehaviorAnalysis() {
            const totalTests = testResults.length;
            const successfulTests = testResults.filter(t => t.result.sent).length;
            const failedTests = totalTests - successfulTests;
            
            const analysis = document.getElementById('behavior-analysis');
            analysis.innerHTML = `
                <h4>📊 إحصائيات الاختبار:</h4>
                <ul>
                    <li><strong>إجمالي الاختبارات:</strong> ${totalTests}</li>
                    <li><strong>نجح إرسال الكود:</strong> ${successfulTests} (${((successfulTests/totalTests)*100).toFixed(1)}%)</li>
                    <li><strong>فشل الإرسال:</strong> ${failedTests} (${((failedTests/totalTests)*100).toFixed(1)}%)</li>
                </ul>
                
                <h4>🔍 ملاحظات:</h4>
                <ul>
                    <li>Telegram يتحقق من صحة تنسيق الرقم أولاً</li>
                    <li>يرفض الأرقام الوهمية المعروفة</li>
                    <li>يتحقق من تسجيل الرقم في النظام</li>
                    <li>يرسل الكود فقط للأرقام المسجلة</li>
                    <li>وقت الإرسال يتراوح بين 1-6 ثواني</li>
                </ul>
            `;
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('🧪 محاكي اختبار Telegram جاهز');
        };
    </script>
</body>
</html>
