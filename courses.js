// الحصول على مراجع للعناصر
const coursesListElement = document.getElementById('courses-list');
const loadingMessageElement = document.getElementById('loading-message');
const errorMessageElement = document.getElementById('error-message');
const backBtn = document.getElementById('back-btn');
const addCourseBtn = document.getElementById('add-course-btn');

// التحقق من حالة تسجيل الدخول
const userEmail = window.localStorage.getItem('userEmail');
if (!userEmail) {
    // المستخدم غير مسجل الدخول، توجيه إلى صفحة تسجيل الدخول
    window.location.href = 'index.html';
}

// إضافة مستمع حدث لزر العودة
backBtn.addEventListener('click', function() {
    window.location.href = 'welcome.html';
});

// إضافة مستمع حدث لزر إضافة كورس جديد
addCourseBtn.addEventListener('click', function() {
    window.location.href = 'add-course.html';
});

// دالة لعرض الكورسات
function displayCourses(courses) {
    // إخفاء رسالة التحميل
    loadingMessageElement.style.display = 'none';

    // التحقق من وجود كورسات
    if (!courses || courses.length === 0) {
        coursesListElement.innerHTML = '<div class="no-courses">لا توجد كورسات متاحة حاليًا</div>';

        // عرض رسالة نجاح
        const successMessage = document.createElement('div');
        successMessage.className = 'success-message';
        successMessage.textContent = 'تم الاتصال بقاعدة البيانات بنجاح، لكن لا توجد كورسات متاحة حاليًا.';
        coursesListElement.appendChild(successMessage);
        return;
    }

    // إنشاء عنصر HTML لكل كورس
    let coursesHTML = '';
    courses.forEach(course => {
        coursesHTML += `
            <div class="course-item">
                <div class="course-title">${course.title || 'بدون عنوان'}</div>
                <div class="course-description">${course.description || 'لا يوجد وصف'}</div>
                <div class="course-instructor">المدرس: ${course.instructor || 'غير محدد'}</div>
                <div class="course-details">
                    <span>المدة: ${course.duration || 'غير محدد'}</span>
                    <span>المستوى: ${course.level || 'غير محدد'}</span>
                    <span>السعر: ${course.price || 'مجاني'}</span>
                </div>
            </div>
        `;
    });

    // عرض الكورسات
    coursesListElement.innerHTML = coursesHTML;

    // عرض رسالة نجاح
    const successMessage = document.createElement('div');
    successMessage.className = 'success-message';
    successMessage.textContent = `تم الاتصال بقاعدة البيانات بنجاح وعرض ${courses.length} كورس.`;
    coursesListElement.appendChild(successMessage);
}

// دالة لعرض رسالة خطأ
function displayError(message) {
    // إخفاء رسالة التحميل
    loadingMessageElement.style.display = 'none';

    // عرض رسالة الخطأ
    errorMessageElement.textContent = message;
    errorMessageElement.style.display = 'block';

    // إضافة تفاصيل إضافية
    const errorDetails = document.createElement('div');
    errorDetails.className = 'error-details';
    errorDetails.innerHTML = `
        <p>تفاصيل الخطأ:</p>
        <ul>
            <li>فشل الاتصال بقاعدة البيانات</li>
            <li>تأكد من تشغيل الخادم الوسيط</li>
            <li>تأكد من صحة إعدادات Firebase</li>
        </ul>
    `;
    errorMessageElement.appendChild(errorDetails);
}

// الحصول على قائمة الكورسات
getCourses()
    .then(courses => {
        // عرض الكورسات
        displayCourses(courses);
    })
    .catch(error => {
        console.error('خطأ في جلب الكورسات:', error);

        // عرض رسالة الخطأ
        displayError('حدث خطأ أثناء جلب الكورسات. يرجى المحاولة مرة أخرى.');
    });
