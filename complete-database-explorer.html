<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستكشف قواعد البيانات الشامل - Firebase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }

        .discover-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .discover-btn:hover {
            background: #3367d6;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }

        .discover-btn.realtime {
            background: #ff6b6b;
        }

        .discover-btn.realtime:hover {
            background: #ee5a24;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4285f4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .project-info {
            padding: 20px;
            background: #e8f5e8;
            text-align: center;
            border-bottom: 1px solid #c8e6c9;
        }

        .project-id {
            font-size: 1.5em;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .summary-section {
            padding: 20px;
            background: #fff3e0;
            border-bottom: 1px solid #ffcc02;
        }

        .summary-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #e65100;
            margin-bottom: 15px;
            text-align: center;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-right: 4px solid #ff9800;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .summary-card h4 {
            color: #e65100;
            margin-bottom: 10px;
        }

        .summary-card .count {
            font-size: 2em;
            font-weight: bold;
            color: #ff5722;
        }

        .databases-container {
            padding: 30px;
        }

        .database-section {
            margin-bottom: 40px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
        }

        .database-section.firestore {
            border-color: #4285f4;
        }

        .database-section.realtime {
            border-color: #ff6b6b;
        }

        .database-header {
            padding: 20px;
            color: white;
        }

        .database-header.firestore {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
        }

        .database-header.realtime {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .database-name {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .database-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .database-type {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .database-status {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .database-body {
            padding: 25px;
        }

        .links-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .link-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-right: 4px solid #4285f4;
            transition: all 0.3s ease;
        }

        .link-card.realtime {
            border-right-color: #ff6b6b;
        }

        .link-card:hover {
            background: #e3f2fd;
            transform: translateX(-5px);
        }

        .link-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .link-url {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            word-break: break-all;
            font-size: 0.9em;
            color: #666;
            margin-bottom: 10px;
        }

        .link-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #3367d6;
        }

        .action-btn.copy {
            background: #34a853;
        }

        .action-btn.copy:hover {
            background: #2e7d32;
        }

        .action-btn.test {
            background: #ea4335;
        }

        .action-btn.test:hover {
            background: #d33b2c;
        }

        .action-btn.realtime {
            background: #ff6b6b;
        }

        .action-btn.realtime:hover {
            background: #ee5a24;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #d32f2f;
            background: #ffebee;
            margin: 20px;
            border-radius: 8px;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
            font-style: italic;
        }

        .copy-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .copy-notification.show {
            transform: translateX(0);
        }

        .auth-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .auth-warning strong {
            color: #d63031;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ مستكشف قواعد البيانات الشامل</h1>
            <p>اكتشاف وعرض جميع قواعد البيانات في مشروع Firebase (Firestore + Realtime Database)</p>
        </div>

        <div class="controls">
            <button class="discover-btn" onclick="discoverFirestore()">
                🔍 اكتشاف Firestore
            </button>
            <button class="discover-btn realtime" onclick="discoverRealtimeDB()">
                🔥 اكتشاف Realtime Database
            </button>
            <button class="discover-btn" onclick="discoverAll()">
                🚀 اكتشاف الكل
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            جاري اكتشاف قواعد البيانات...
        </div>

        <div id="project-info" class="project-info" style="display: none;">
            <div class="project-id" id="project-id"></div>
        </div>

        <div id="summary-section" class="summary-section" style="display: none;">
            <div class="summary-title">📊 ملخص قواعد البيانات المكتشفة</div>
            <div class="summary-grid" id="summary-grid"></div>
        </div>

        <div id="databases-container" class="databases-container"></div>

        <div id="error-container" style="display: none;"></div>
    </div>

    <div id="copy-notification" class="copy-notification">
        تم نسخ الرابط! 📋
    </div>

    <script>
        let firestoreData = null;
        let realtimeData = null;

        // اكتشاف جميع قواعد البيانات عند فتح الصفحة
        window.onload = function() {
            discoverAll();
        };

        async function discoverFirestore() {
            showLoading('جاري اكتشاف Firestore...');
            
            try {
                const response = await fetch('http://localhost:3000/api/discover-databases');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                firestoreData = await response.json();
                console.log('📊 Firestore Data:', firestoreData);
                
                hideLoading();
                updateDisplay();

            } catch (error) {
                hideLoading();
                showError('خطأ في اكتشاف Firestore: ' + error.message);
                console.error('خطأ في اكتشاف Firestore:', error);
            }
        }

        async function discoverRealtimeDB() {
            showLoading('جاري اكتشاف Realtime Database...');
            
            try {
                const response = await fetch('http://localhost:3000/api/discover-realtime-db');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                realtimeData = await response.json();
                console.log('🔥 Realtime DB Data:', realtimeData);
                
                hideLoading();
                updateDisplay();

            } catch (error) {
                hideLoading();
                showError('خطأ في اكتشاف Realtime Database: ' + error.message);
                console.error('خطأ في اكتشاف Realtime Database:', error);
            }
        }

        async function discoverAll() {
            showLoading('جاري اكتشاف جميع قواعد البيانات...');
            
            try {
                // اكتشاف Firestore
                const firestoreResponse = await fetch('http://localhost:3000/api/discover-databases');
                if (firestoreResponse.ok) {
                    firestoreData = await firestoreResponse.json();
                }

                // اكتشاف Realtime Database
                const realtimeResponse = await fetch('http://localhost:3000/api/discover-realtime-db');
                if (realtimeResponse.ok) {
                    realtimeData = await realtimeResponse.json();
                }

                hideLoading();
                updateDisplay();

            } catch (error) {
                hideLoading();
                showError('خطأ في اكتشاف قواعد البيانات: ' + error.message);
                console.error('خطأ في اكتشاف قواعد البيانات:', error);
            }
        }

        function updateDisplay() {
            displayProjectInfo();
            displaySummary();
            displayDatabases();
        }

        function displayProjectInfo() {
            const projectInfo = document.getElementById('project-info');
            const projectId = document.getElementById('project-id');

            const projectName = firestoreData?.projectId || realtimeData?.projectId || 'غير معروف';
            projectId.textContent = `مشروع: ${projectName}`;

            projectInfo.style.display = 'block';
        }

        function displaySummary() {
            const summarySection = document.getElementById('summary-section');
            const summaryGrid = document.getElementById('summary-grid');

            let summaryHTML = '';

            // إحصائيات Firestore
            if (firestoreData) {
                summaryHTML += `
                    <div class="summary-card">
                        <h4>🗄️ Firestore</h4>
                        <div class="count">${firestoreData.totalDatabases}</div>
                        <div>قاعدة بيانات</div>
                    </div>
                `;
            }

            // إحصائيات Realtime Database
            if (realtimeData) {
                const status = realtimeData.accessible ? 'متاح' : 'يتطلب مصادقة';
                summaryHTML += `
                    <div class="summary-card">
                        <h4>🔥 Realtime Database</h4>
                        <div class="count">1</div>
                        <div>${status}</div>
                    </div>
                `;
            }

            // إجمالي
            const totalDatabases = (firestoreData?.totalDatabases || 0) + (realtimeData ? 1 : 0);
            summaryHTML += `
                <div class="summary-card">
                    <h4>📊 الإجمالي</h4>
                    <div class="count">${totalDatabases}</div>
                    <div>قاعدة بيانات</div>
                </div>
            `;

            summaryGrid.innerHTML = summaryHTML;
            summarySection.style.display = 'block';
        }

        function displayDatabases() {
            const container = document.getElementById('databases-container');
            let databasesHTML = '';

            // عرض Firestore
            if (firestoreData && firestoreData.databases) {
                firestoreData.databases.forEach(db => {
                    databasesHTML += `
                        <div class="database-section firestore">
                            <div class="database-header firestore">
                                <div class="database-name">🗄️ Firestore: ${db.name}</div>
                                <div class="database-info">
                                    <div class="database-type">${db.type}</div>
                                    <div class="database-status">📍 ${db.locationId}</div>
                                </div>
                            </div>
                            <div class="database-body">
                                ${generateFirestoreLinks(db)}
                            </div>
                        </div>
                    `;
                });
            }

            // عرض Realtime Database
            if (realtimeData) {
                const statusText = realtimeData.accessible ? 'متاح' : 'يتطلب مصادقة';
                const statusColor = realtimeData.accessible ? '#4caf50' : '#ff9800';
                
                databasesHTML += `
                    <div class="database-section realtime">
                        <div class="database-header realtime">
                            <div class="database-name">🔥 Realtime Database</div>
                            <div class="database-info">
                                <div class="database-type">JSON Database</div>
                                <div class="database-status" style="color: ${statusColor}">🔐 ${statusText}</div>
                            </div>
                        </div>
                        <div class="database-body">
                            ${generateRealtimeLinks(realtimeData)}
                        </div>
                    </div>
                `;
            }

            if (!databasesHTML) {
                databasesHTML = `
                    <div class="no-data">
                        <h3>📭 لا توجد قواعد بيانات</h3>
                        <p>لم يتم العثور على أي قواعد بيانات في هذا المشروع</p>
                    </div>
                `;
            }

            container.innerHTML = databasesHTML;
        }

        function generateFirestoreLinks(db) {
            return `
                <div class="links-section">
                    <div class="link-card">
                        <div class="link-title">🔗 جميع المستندات</div>
                        <div class="link-url">${db.documentsUrl}</div>
                        <div class="link-actions">
                            <button class="action-btn copy" onclick="copyToClipboard('${db.documentsUrl}')">📋 نسخ</button>
                            <button class="action-btn test" onclick="testDirectLink('${db.documentsUrl}')">🧪 اختبار</button>
                        </div>
                    </div>
                    <div class="link-card">
                        <div class="link-title">📚 الكورسات</div>
                        <div class="link-url">${db.coursesUrl}</div>
                        <div class="link-actions">
                            <button class="action-btn copy" onclick="copyToClipboard('${db.coursesUrl}')">📋 نسخ</button>
                            <button class="action-btn test" onclick="testDirectLink('${db.coursesUrl}')">🧪 اختبار</button>
                        </div>
                    </div>
                    <div class="link-card">
                        <div class="link-title">👤 المستخدمون</div>
                        <div class="link-url">${db.usersUrl}</div>
                        <div class="link-actions">
                            <button class="action-btn copy" onclick="copyToClipboard('${db.usersUrl}')">📋 نسخ</button>
                            <button class="action-btn test" onclick="testDirectLink('${db.usersUrl}')">🧪 اختبار</button>
                        </div>
                    </div>
                    <div class="link-card">
                        <div class="link-title">🚀 API الخادم الوسيط</div>
                        <div class="link-url">${db.apiUrl}</div>
                        <div class="link-actions">
                            <button class="action-btn copy" onclick="copyToClipboard('${db.apiUrl}')">📋 نسخ</button>
                            <button class="action-btn" onclick="loadDatabaseData('${db.name}')">📊 جلب البيانات</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateRealtimeLinks(rtData) {
            const authWarning = !rtData.accessible ? `
                <div class="auth-warning">
                    <strong>⚠️ تحذير:</strong> Realtime Database يتطلب مصادقة للوصول للبيانات. 
                    الروابط أدناه قد لا تعمل بدون إعداد قواعد الأمان أو المصادقة.
                </div>
            ` : '';

            return `
                ${authWarning}
                <div class="links-section">
                    <div class="link-card realtime">
                        <div class="link-title">🔥 الجذر الرئيسي</div>
                        <div class="link-url">${rtData.links.root}</div>
                        <div class="link-actions">
                            <button class="action-btn copy" onclick="copyToClipboard('${rtData.links.root}')">📋 نسخ</button>
                            <button class="action-btn test realtime" onclick="testDirectLink('${rtData.links.root}')">🧪 اختبار</button>
                        </div>
                    </div>
                    <div class="link-card realtime">
                        <div class="link-title">📚 الكورسات</div>
                        <div class="link-url">${rtData.links.courses}</div>
                        <div class="link-actions">
                            <button class="action-btn copy" onclick="copyToClipboard('${rtData.links.courses}')">📋 نسخ</button>
                            <button class="action-btn test realtime" onclick="testDirectLink('${rtData.links.courses}')">🧪 اختبار</button>
                        </div>
                    </div>
                    <div class="link-card realtime">
                        <div class="link-title">👤 المستخدمون</div>
                        <div class="link-url">${rtData.links.users}</div>
                        <div class="link-actions">
                            <button class="action-btn copy" onclick="copyToClipboard('${rtData.links.users}')">📋 نسخ</button>
                            <button class="action-btn test realtime" onclick="testDirectLink('${rtData.links.users}')">🧪 اختبار</button>
                        </div>
                    </div>
                    <div class="link-card realtime">
                        <div class="link-title">🎛️ وحة التحكم</div>
                        <div class="link-url">${rtData.links.console}</div>
                        <div class="link-actions">
                            <button class="action-btn copy" onclick="copyToClipboard('${rtData.links.console}')">📋 نسخ</button>
                            <button class="action-btn" onclick="window.open('${rtData.links.console}', '_blank')">🌐 فتح</button>
                        </div>
                    </div>
                </div>
            `;
        }

        async function testDirectLink(url) {
            try {
                console.log('🧪 اختبار الرابط المباشر:', url);
                
                const response = await fetch(url);
                
                if (response.ok) {
                    const data = await response.json();
                    const dataCount = data && typeof data === 'object' ? 
                        (Array.isArray(data) ? data.length : Object.keys(data).length) : 0;
                    alert(`✅ الرابط يعمل!\nعدد العناصر: ${dataCount}`);
                } else if (response.status === 401) {
                    alert(`🔐 الرابط يتطلب مصادقة!\nكود الخطأ: ${response.status}`);
                } else {
                    alert(`❌ الرابط لا يعمل!\nكود الخطأ: ${response.status}`);
                }
            } catch (error) {
                alert(`❌ خطأ في الاتصال: ${error.message}`);
            }
        }

        async function loadDatabaseData(dbName) {
            try {
                const response = await fetch(`http://localhost:3000/api/database/${dbName}/all-data`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`📊 بيانات قاعدة البيانات ${dbName}:`, data);
                alert(`📊 تم جلب البيانات بنجاح!\nعدد المستندات: ${data.summary?.totalDocuments || 0}`);

            } catch (error) {
                alert(`❌ خطأ في جلب البيانات: ${error.message}`);
            }
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showCopyNotification();
            }).catch(err => {
                console.error('خطأ في النسخ:', err);
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showCopyNotification();
            });
        }

        function showCopyNotification() {
            const notification = document.getElementById('copy-notification');
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 2000);
        }

        function showLoading(message) {
            const loading = document.getElementById('loading');
            loading.querySelector('div:last-child').textContent = message;
            loading.style.display = 'block';
            
            document.getElementById('databases-container').innerHTML = '';
            document.getElementById('error-container').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showError(message) {
            const errorContainer = document.getElementById('error-container');
            errorContainer.innerHTML = `
                <div class="error">
                    <h3>❌ حدث خطأ</h3>
                    <p>${message}</p>
                    <button class="discover-btn" onclick="discoverAll()" style="margin-top: 15px;">
                        🔄 إعادة المحاولة
                    </button>
                </div>
            `;
            errorContainer.style.display = 'block';
        }
    </script>
</body>
</html>
