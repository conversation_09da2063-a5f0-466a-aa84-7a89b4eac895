// الحصول على مراجع للعناصر
const signupForm = document.getElementById('signup-form');
const emailInput = document.getElementById('email');
const errorMessage = document.getElementById('error-message');

// التحقق مما إذا كان المستخدم مسجل الدخول بالفعل
firebase.auth().onAuthStateChanged(function(user) {
    if (user) {
        // المستخدم مسجل الدخول، توجيه إلى صفحة الترحيب
        window.location.href = 'welcome.html';
    }
});

// إضافة مستمع حدث لنموذج إنشاء الحساب
signupForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const email = emailInput.value;

    // التحقق من صحة البريد الإلكتروني
    if (!email || !email.includes('@')) {
        errorMessage.textContent = 'يرجى إدخال بريد إلكتروني صالح';
        errorMessage.style.color = '#d93025';
        return;
    }

    // إظهار رسالة تحميل
    errorMessage.textContent = 'جاري إرسال رابط تسجيل الدخول إلى بريدك الإلكتروني...';
    errorMessage.style.color = '#4285f4';

    // تسجيل الدخول بالبريد الإلكتروني فقط باستخدام الخادم الوسيط
    console.log('محاولة إنشاء حساب جديد بالبريد الإلكتروني:', email);

    // عرض رسالة تحميل
    errorMessage.textContent = 'جاري إنشاء الحساب...';
    errorMessage.style.color = '#4285f4';

    // استدعاء دالة تسجيل الدخول
    loginWithEmail(email)
        .then(data => {
            console.log('تم إنشاء الحساب بنجاح:', data);

            // تخزين البريد الإلكتروني في التخزين المحلي
            window.localStorage.setItem('userEmail', email);

            // إنشاء بيانات المستخدم الافتراضية
            const userData = {
                fullName: 'مستخدم جديد',
                age: '25',
                country: 'غير محدد',
                interests: 'غير محدد',
                registrationDate: new Date().toLocaleDateString('ar-EG')
            };

            // حفظ بيانات المستخدم في قاعدة البيانات
            saveUserData(email, userData)
                .then(() => {
                    // عرض رسالة نجاح
                    errorMessage.textContent = 'تم إنشاء الحساب بنجاح!';
                    errorMessage.style.color = 'green';

                    // توجيه المستخدم إلى صفحة الترحيب بعد ثانيتين
                    setTimeout(() => {
                        window.location.href = 'welcome.html';
                    }, 2000);
                })
                .catch(error => {
                    console.error('خطأ في حفظ بيانات المستخدم:', error);

                    // توجيه المستخدم إلى صفحة الترحيب على أي حال
                    setTimeout(() => {
                        window.location.href = 'welcome.html';
                    }, 2000);
                });
        })
        .catch(error => {
            console.error('خطأ في إنشاء الحساب:', error);

            // عرض رسالة الخطأ
            errorMessage.textContent = error.message || 'فشل إنشاء الحساب. يرجى المحاولة مرة أخرى.';
            errorMessage.style.color = '#d93025';
        });
});
