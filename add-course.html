<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة كورس جديد</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .add-course-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        .buttons-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .submit-btn {
            background-color: #0f9d58;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .submit-btn:hover {
            background-color: #0b8043;
        }
        
        .cancel-btn {
            background-color: #d93025;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .cancel-btn:hover {
            background-color: #b7261d;
        }
        
        .success-message {
            text-align: center;
            padding: 15px;
            color: #0f9d58;
            background-color: #e6f4ea;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #0f9d58;
            display: none;
        }
        
        .error-message {
            text-align: center;
            padding: 15px;
            color: #d93025;
            background-color: #fce8e6;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #d93025;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="add-course-container">
            <h2>إضافة كورس جديد</h2>
            
            <div id="success-message" class="success-message"></div>
            <div id="error-message" class="error-message"></div>
            
            <form id="add-course-form">
                <div class="form-group">
                    <label for="title">عنوان الكورس *</label>
                    <input type="text" id="title" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="description">وصف الكورس</label>
                    <textarea id="description" name="description"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="instructor">اسم المدرس</label>
                    <input type="text" id="instructor" name="instructor">
                </div>
                
                <div class="form-group">
                    <label for="duration">مدة الكورس</label>
                    <input type="text" id="duration" name="duration" placeholder="مثال: 8 أسابيع">
                </div>
                
                <div class="form-group">
                    <label for="level">مستوى الكورس</label>
                    <select id="level" name="level">
                        <option value="مبتدئ">مبتدئ</option>
                        <option value="متوسط">متوسط</option>
                        <option value="متقدم">متقدم</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="price">سعر الكورس</label>
                    <input type="text" id="price" name="price" placeholder="مثال: مجاني، 50 دولار">
                </div>
                
                <div class="buttons-container">
                    <button type="button" id="cancel-btn" class="cancel-btn">إلغاء</button>
                    <button type="submit" class="submit-btn">إضافة الكورس</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
    
    <!-- تكوين Firebase والتطبيق -->
    <script src="firebase-config.js"></script>
    <script src="firebase-db.js"></script>
    <script src="add-course.js"></script>
</body>
</html>
