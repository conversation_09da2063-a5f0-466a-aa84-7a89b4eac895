<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض جميع بيانات قاعدة البيانات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }

        .refresh-btn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .refresh-btn:hover {
            background: #ee5a24;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #ff6b6b;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats {
            padding: 20px;
            background: #e8f5e8;
            display: flex;
            justify-content: space-around;
            text-align: center;
            flex-wrap: wrap;
        }

        .stat-item {
            flex: 1;
            min-width: 150px;
            margin: 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #34a853;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .collections-container {
            padding: 30px;
        }

        .collection-section {
            margin-bottom: 40px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
        }

        .collection-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.5em;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .collection-title {
            flex: 1;
        }

        .collection-actions {
            display: flex;
            gap: 10px;
        }

        .delete-btn {
            background: #ea4335;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .delete-btn:hover {
            background: #d33b2c;
            transform: scale(1.05);
        }

        .delete-btn:active {
            transform: scale(0.95);
        }

        .documents-grid {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .document-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .document-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .document-header {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            color: #2d3436;
            padding: 15px;
        }

        .document-id {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .document-path {
            font-size: 0.9em;
            opacity: 0.8;
            font-family: monospace;
        }

        .document-body {
            padding: 20px;
        }

        .field-item {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .field-label {
            font-weight: bold;
            color: #333;
            min-width: 100px;
            margin-left: 10px;
        }

        .field-value {
            color: #666;
            flex: 1;
            word-break: break-word;
        }

        .field-type {
            background: #e9ecef;
            color: #495057;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-right: 10px;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #d32f2f;
            background: #ffebee;
            margin: 20px;
            border-radius: 8px;
        }

        .raw-data {
            margin: 20px;
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .raw-data h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
            font-style: italic;
        }

        .search-summary {
            margin: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .search-summary h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .summary-section {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-right: 4px solid #4285f4;
        }

        .summary-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .summary-list {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ عارض جميع بيانات قاعدة البيانات</h1>
            <p>عرض شامل لجميع المجموعات والمستندات في Firestore</p>
        </div>

        <div class="controls">
            <button class="refresh-btn" onclick="loadAllData()">🔄 تحديث جميع البيانات</button>
            <button class="refresh-btn" onclick="loadCoursesOnly()">📚 الكورسات فقط</button>
            <button class="refresh-btn" onclick="toggleRawData()">📄 البيانات الخام</button>
        </div>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            جاري تحميل جميع البيانات من Firestore...
        </div>

        <div id="stats" class="stats" style="display: none;">
            <div class="stat-item">
                <div class="stat-number" id="total-documents">0</div>
                <div class="stat-label">إجمالي المستندات</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="total-collections">0</div>
                <div class="stat-label">إجمالي المجموعات</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="courses-count">0</div>
                <div class="stat-label">الكورسات</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="users-count">0</div>
                <div class="stat-label">المستخدمون</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="test-count">0</div>
                <div class="stat-label">بيانات تجريبية</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="searched-count">0</div>
                <div class="stat-label">مجموعات مفحوصة</div>
            </div>
        </div>

        <div id="search-summary" class="search-summary" style="display: none;">
            <h3>📋 ملخص البحث المفصل</h3>
            <div id="summary-content"></div>
        </div>

        <div id="collections-container" class="collections-container"></div>

        <div id="error-container" style="display: none;"></div>

        <div id="raw-data" class="raw-data" style="display: none;">
            <h3>البيانات الخام من Firestore:</h3>
            <div id="json-viewer" class="json-viewer"></div>
        </div>
    </div>

    <script>
        let rawDataVisible = false;
        let lastResponse = null;

        // تحميل البيانات عند فتح الصفحة
        window.onload = function() {
            loadAllData();
        };

        async function loadAllData() {
            const loading = document.getElementById('loading');
            const collectionsContainer = document.getElementById('collections-container');
            const errorContainer = document.getElementById('error-container');
            const stats = document.getElementById('stats');

            // إظهار التحميل
            loading.style.display = 'block';
            collectionsContainer.innerHTML = '';
            errorContainer.style.display = 'none';
            stats.style.display = 'none';

            try {
                const response = await fetch('http://localhost:3000/api/all-data');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                lastResponse = data;

                // إخفاء التحميل
                loading.style.display = 'none';

                // عرض الإحصائيات
                displayStats(data);

                // عرض البيانات
                displayAllData(data);

                // تحديث البيانات الخام
                updateRawData(data);

            } catch (error) {
                loading.style.display = 'none';
                showError(error.message);
            }
        }

        async function loadCoursesOnly() {
            const loading = document.getElementById('loading');
            const collectionsContainer = document.getElementById('collections-container');
            const errorContainer = document.getElementById('error-container');
            const stats = document.getElementById('stats');

            // إظهار التحميل
            loading.style.display = 'block';
            collectionsContainer.innerHTML = '';
            errorContainer.style.display = 'none';
            stats.style.display = 'none';

            try {
                const response = await fetch('http://localhost:3000/api/courses');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                lastResponse = data;

                // إخفاء التحميل
                loading.style.display = 'none';

                // عرض الإحصائيات
                displayStats(data);

                // عرض البيانات
                displayAllData(data);

                // تحديث البيانات الخام
                updateRawData(data);

            } catch (error) {
                loading.style.display = 'none';
                showError(error.message);
            }
        }

        function displayStats(data) {
            const stats = document.getElementById('stats');

            // استخدام البيانات الجديدة إذا كانت متوفرة
            if (data.summary) {
                // البيانات الجديدة مع ملخص مفصل
                document.getElementById('total-documents').textContent = data.summary.totalDocuments;
                document.getElementById('total-collections').textContent = data.summary.foundCollections;

                // البحث عن الكورسات والمستخدمين والبيانات التجريبية
                let coursesCount = 0;
                let usersCount = 0;
                let testCount = 0;

                if (data.foundCollections) {
                    data.foundCollections.forEach(collection => {
                        if (collection.name === 'courses') coursesCount = collection.count;
                        if (collection.name === 'users') usersCount = collection.count;
                        if (collection.name === 'test') testCount = collection.count;
                    });
                }

                document.getElementById('courses-count').textContent = coursesCount;
                document.getElementById('users-count').textContent = usersCount;
                document.getElementById('test-count').textContent = testCount;
                document.getElementById('searched-count').textContent = data.summary.totalSearched;

                console.log('📊 إحصائيات مفصلة:', data.summary);
                console.log('📁 المجموعات الموجودة:', data.foundCollections);
                console.log('📭 المجموعات الفارغة:', data.emptyCollections);

                // عرض ملخص البحث
                displaySearchSummary(data);
            } else {
                // البيانات القديمة
                const totalDocuments = data.documents ? data.documents.length : 0;

                // تجميع البيانات حسب المجموعة
                const collections = {};
                let coursesCount = 0;
                let usersCount = 0;

                if (data.documents) {
                    data.documents.forEach(doc => {
                        const pathParts = doc.name.split('/');
                        const collectionName = pathParts[pathParts.length - 2];

                        if (!collections[collectionName]) {
                            collections[collectionName] = 0;
                        }
                        collections[collectionName]++;

                        if (collectionName === 'courses') coursesCount++;
                        if (collectionName === 'users') usersCount++;
                    });
                }

                document.getElementById('total-documents').textContent = totalDocuments;
                document.getElementById('total-collections').textContent = Object.keys(collections).length;
                document.getElementById('courses-count').textContent = coursesCount;
                document.getElementById('users-count').textContent = usersCount;
            }

            stats.style.display = 'flex';
        }

        function displayAllData(data) {
            const container = document.getElementById('collections-container');

            if (!data.documents || data.documents.length === 0) {
                container.innerHTML = `
                    <div class="no-data">
                        <h3>📭 لا توجد بيانات في قاعدة البيانات</h3>
                        <p>يمكنك إضافة بيانات جديدة من خلال التطبيق</p>
                    </div>
                `;
                return;
            }

            // تجميع المستندات حسب المجموعة
            const collections = {};
            data.documents.forEach(doc => {
                const pathParts = doc.name.split('/');
                const collectionName = pathParts[pathParts.length - 2];

                if (!collections[collectionName]) {
                    collections[collectionName] = [];
                }
                collections[collectionName].push(doc);
            });

            // عرض كل مجموعة
            container.innerHTML = Object.keys(collections).map(collectionName => {
                const documents = collections[collectionName];

                return `
                    <div class="collection-section" id="collection-${collectionName}">
                        <div class="collection-header">
                            <div class="collection-title">
                                📁 مجموعة: ${collectionName} (${documents.length} مستند)
                            </div>
                            <div class="collection-actions">
                                <button class="delete-btn" onclick="deleteCollection('${collectionName}')">
                                    🗑️ حذف المجموعة
                                </button>
                            </div>
                        </div>
                        <div class="documents-grid">
                            ${documents.map(doc => {
                                const documentId = doc.name.split('/').pop();
                                const fields = doc.fields || {};

                                return `
                                    <div class="document-card">
                                        <div class="document-header">
                                            <div class="document-id">📄 ${documentId}</div>
                                            <div class="document-path">${doc.name}</div>
                                        </div>
                                        <div class="document-body">
                                            ${Object.keys(fields).map(fieldName => {
                                                const field = fields[fieldName];
                                                const value = getFieldValue(field);
                                                const type = getFieldType(field);

                                                return `
                                                    <div class="field-item">
                                                        <span class="field-type">${type}</span>
                                                        <span class="field-label">${fieldName}:</span>
                                                        <span class="field-value">${value}</span>
                                                    </div>
                                                `;
                                            }).join('')}
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getFieldValue(field) {
            if (field.stringValue !== undefined) return field.stringValue;
            if (field.integerValue !== undefined) return field.integerValue;
            if (field.doubleValue !== undefined) return field.doubleValue;
            if (field.booleanValue !== undefined) return field.booleanValue.toString();
            if (field.timestampValue !== undefined) return field.timestampValue;
            if (field.arrayValue !== undefined) return JSON.stringify(field.arrayValue);
            if (field.mapValue !== undefined) return JSON.stringify(field.mapValue);
            return 'قيمة غير معروفة';
        }

        function getFieldType(field) {
            if (field.stringValue !== undefined) return 'نص';
            if (field.integerValue !== undefined) return 'رقم صحيح';
            if (field.doubleValue !== undefined) return 'رقم عشري';
            if (field.booleanValue !== undefined) return 'منطقي';
            if (field.timestampValue !== undefined) return 'وقت';
            if (field.arrayValue !== undefined) return 'مصفوفة';
            if (field.mapValue !== undefined) return 'كائن';
            return 'غير معروف';
        }

        function showError(message) {
            const errorContainer = document.getElementById('error-container');
            errorContainer.innerHTML = `
                <div class="error">
                    <h3>❌ حدث خطأ في تحميل البيانات</h3>
                    <p>${message}</p>
                    <button class="refresh-btn" onclick="loadAllData()" style="margin-top: 15px;">
                        🔄 إعادة المحاولة
                    </button>
                </div>
            `;
            errorContainer.style.display = 'block';
        }

        function toggleRawData() {
            const rawData = document.getElementById('raw-data');
            rawDataVisible = !rawDataVisible;

            if (rawDataVisible) {
                rawData.style.display = 'block';
                document.querySelector('.refresh-btn:nth-child(3)').textContent = '🙈 إخفاء البيانات الخام';
            } else {
                rawData.style.display = 'none';
                document.querySelector('.refresh-btn:nth-child(3)').textContent = '📄 البيانات الخام';
            }
        }

        function displaySearchSummary(data) {
            const searchSummary = document.getElementById('search-summary');
            const summaryContent = document.getElementById('summary-content');

            if (data.summary && data.foundCollections) {
                let summaryHTML = '';

                // المجموعات التي تحتوي على بيانات
                if (data.foundCollections.length > 0) {
                    summaryHTML += `
                        <div class="summary-section">
                            <div class="summary-title">✅ المجموعات التي تحتوي على بيانات (${data.foundCollections.length}):</div>
                            <div class="summary-list">
                                ${data.foundCollections.map(col => `• ${col.name}: ${col.count} مستند`).join('<br>')}
                            </div>
                        </div>
                    `;
                }

                // المجموعات الفارغة (عرض أول 10 فقط)
                if (data.emptyCollections && data.emptyCollections.length > 0) {
                    const displayEmpty = data.emptyCollections.slice(0, 10);
                    const remainingEmpty = data.emptyCollections.length - 10;

                    summaryHTML += `
                        <div class="summary-section">
                            <div class="summary-title">📭 المجموعات الفارغة (${data.emptyCollections.length}):</div>
                            <div class="summary-list">
                                ${displayEmpty.map(col => `• ${col}`).join('<br>')}
                                ${remainingEmpty > 0 ? `<br>... و ${remainingEmpty} مجموعة أخرى` : ''}
                            </div>
                        </div>
                    `;
                }

                // المجموعات التي بها أخطاء
                if (data.errorCollections && data.errorCollections.length > 0) {
                    summaryHTML += `
                        <div class="summary-section">
                            <div class="summary-title">❌ المجموعات التي بها أخطاء (${data.errorCollections.length}):</div>
                            <div class="summary-list">
                                ${data.errorCollections.map(col => `• ${col.name}: ${col.error}`).join('<br>')}
                            </div>
                        </div>
                    `;
                }

                summaryContent.innerHTML = summaryHTML;
                searchSummary.style.display = 'block';
            } else {
                searchSummary.style.display = 'none';
            }
        }

        async function deleteCollection(collectionName) {
            // تأكيد الحذف
            const confirmDelete = confirm(
                `⚠️ تحذير!\n\n` +
                `هل أنت متأكد من حذف مجموعة "${collectionName}" بالكامل؟\n\n` +
                `هذا الإجراء سيحذف جميع المستندات في هذه المجموعة ولا يمكن التراجع عنه!\n\n` +
                `اكتب "نعم" للتأكيد:`
            );

            if (!confirmDelete) {
                return;
            }

            // تأكيد إضافي
            const finalConfirm = prompt(
                `للتأكيد النهائي، اكتب اسم المجموعة "${collectionName}" بالضبط:`
            );

            if (finalConfirm !== collectionName) {
                alert('❌ تم إلغاء العملية. اسم المجموعة غير صحيح.');
                return;
            }

            try {
                // إظهار حالة التحميل
                const collectionElement = document.getElementById(`collection-${collectionName}`);
                const deleteBtn = collectionElement.querySelector('.delete-btn');
                const originalText = deleteBtn.textContent;

                deleteBtn.textContent = '🔄 جاري الحذف...';
                deleteBtn.disabled = true;
                deleteBtn.style.background = '#ffa500';

                console.log(`🗑️ بدء حذف مجموعة: ${collectionName}`);

                // إرسال طلب الحذف للخادم الوسيط
                const response = await fetch(`http://localhost:3000/api/delete-collection/${collectionName}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('✅ نتيجة الحذف:', result);

                // إظهار النتيجة
                if (result.success) {
                    alert(`✅ تم حذف مجموعة "${collectionName}" بنجاح!\n\nتم حذف ${result.deletedCount} مستند.`);

                    // إزالة المجموعة من الواجهة
                    collectionElement.style.transition = 'all 0.5s ease';
                    collectionElement.style.opacity = '0';
                    collectionElement.style.transform = 'scale(0.8)';

                    setTimeout(() => {
                        collectionElement.remove();
                        // تحديث الإحصائيات
                        loadAllData();
                    }, 500);
                } else {
                    throw new Error(result.error || 'فشل في حذف المجموعة');
                }

            } catch (error) {
                console.error('خطأ في حذف المجموعة:', error);
                alert(`❌ فشل في حذف المجموعة "${collectionName}":\n\n${error.message}`);

                // إعادة تعيين الزر
                const collectionElement = document.getElementById(`collection-${collectionName}`);
                const deleteBtn = collectionElement.querySelector('.delete-btn');
                deleteBtn.textContent = originalText;
                deleteBtn.disabled = false;
                deleteBtn.style.background = '#ea4335';
            }
        }

        function updateRawData(data) {
            const jsonViewer = document.getElementById('json-viewer');
            jsonViewer.textContent = JSON.stringify(data, null, 2);
        }
    </script>
</body>
</html>
