<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض بيانات Firestore</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }

        .refresh-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #3367d6;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4285f4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats {
            padding: 20px;
            background: #e8f5e8;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }

        .stat-item {
            flex: 1;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #34a853;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .courses-grid {
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .course-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .course-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
        }

        .course-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .course-id {
            font-size: 0.9em;
            opacity: 0.8;
            font-family: monospace;
        }

        .course-body {
            padding: 20px;
        }

        .course-field {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .field-icon {
            width: 20px;
            height: 20px;
            margin-left: 10px;
            opacity: 0.7;
        }

        .field-label {
            font-weight: bold;
            color: #333;
            min-width: 80px;
            margin-left: 10px;
        }

        .field-value {
            color: #666;
            flex: 1;
        }

        .level-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .level-مبتدئ { background: #e8f5e8; color: #2e7d32; }
        .level-متوسط { background: #fff3e0; color: #f57c00; }
        .level-متقدم { background: #ffebee; color: #c62828; }

        .price-tag {
            background: #4285f4;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #d32f2f;
            background: #ffebee;
            margin: 20px;
            border-radius: 8px;
        }

        .raw-data {
            margin: 20px;
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .raw-data h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .toggle-raw {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }

        .toggle-raw:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 عارض بيانات Firestore</h1>
            <p>عرض مباشر لكورسات قاعدة البيانات</p>
        </div>

        <div class="controls">
            <button class="refresh-btn" onclick="loadCourses()">🔄 تحديث البيانات</button>
            <button class="toggle-raw" onclick="toggleRawData()">📄 عرض البيانات الخام</button>
        </div>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            جاري تحميل البيانات من Firestore...
        </div>

        <div id="stats" class="stats" style="display: none;">
            <div class="stat-item">
                <div class="stat-number" id="total-courses">0</div>
                <div class="stat-label">إجمالي الكورسات</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="free-courses">0</div>
                <div class="stat-label">كورسات مجانية</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="paid-courses">0</div>
                <div class="stat-label">كورسات مدفوعة</div>
            </div>
        </div>

        <div id="courses-container" class="courses-grid"></div>

        <div id="error-container" style="display: none;"></div>

        <div id="raw-data" class="raw-data" style="display: none;">
            <h3>البيانات الخام من Firestore:</h3>
            <div id="json-viewer" class="json-viewer"></div>
        </div>
    </div>

    <script>
        let rawDataVisible = false;
        let lastResponse = null;

        // تحميل البيانات عند فتح الصفحة
        window.onload = function() {
            loadCourses();
        };

        async function loadCourses() {
            const loading = document.getElementById('loading');
            const coursesContainer = document.getElementById('courses-container');
            const errorContainer = document.getElementById('error-container');
            const stats = document.getElementById('stats');

            // إظهار التحميل
            loading.style.display = 'block';
            coursesContainer.innerHTML = '';
            errorContainer.style.display = 'none';
            stats.style.display = 'none';

            try {
                const response = await fetch('https://firestore.googleapis.com/v1/projects/e-teaching-8cba6/databases/(default)/documents/courses');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                lastResponse = data;

                // إخفاء التحميل
                loading.style.display = 'none';

                // عرض الإحصائيات
                displayStats(data);

                // عرض الكورسات
                displayCourses(data);

                // تحديث البيانات الخام
                updateRawData(data);

            } catch (error) {
                loading.style.display = 'none';
                showError(error.message);
            }
        }

        function displayStats(data) {
            const stats = document.getElementById('stats');
            const totalCourses = data.documents ? data.documents.length : 0;
            
            let freeCourses = 0;
            let paidCourses = 0;

            if (data.documents) {
                data.documents.forEach(doc => {
                    const price = getFieldValue(doc.fields, 'price');
                    if (price === 'مجاني' || price === '' || !price) {
                        freeCourses++;
                    } else {
                        paidCourses++;
                    }
                });
            }

            document.getElementById('total-courses').textContent = totalCourses;
            document.getElementById('free-courses').textContent = freeCourses;
            document.getElementById('paid-courses').textContent = paidCourses;

            stats.style.display = 'flex';
        }

        function displayCourses(data) {
            const container = document.getElementById('courses-container');

            if (!data.documents || data.documents.length === 0) {
                container.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 50px; color: #666;">
                        <h3>📚 لا توجد كورسات في قاعدة البيانات</h3>
                        <p>يمكنك إضافة كورسات جديدة من خلال التطبيق</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = data.documents.map(doc => {
                const fields = doc.fields || {};
                const courseId = doc.name ? doc.name.split('/').pop() : 'غير معروف';
                
                const title = getFieldValue(fields, 'title') || 'بدون عنوان';
                const description = getFieldValue(fields, 'description') || 'لا يوجد وصف';
                const instructor = getFieldValue(fields, 'instructor') || 'غير محدد';
                const duration = getFieldValue(fields, 'duration') || 'غير محدد';
                const level = getFieldValue(fields, 'level') || 'غير محدد';
                const price = getFieldValue(fields, 'price') || 'مجاني';

                return `
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-title">${title}</div>
                            <div class="course-id">ID: ${courseId}</div>
                        </div>
                        <div class="course-body">
                            <div class="course-field">
                                <span class="field-icon">📝</span>
                                <span class="field-label">الوصف:</span>
                                <span class="field-value">${description}</span>
                            </div>
                            <div class="course-field">
                                <span class="field-icon">👨‍🏫</span>
                                <span class="field-label">المدرس:</span>
                                <span class="field-value">${instructor}</span>
                            </div>
                            <div class="course-field">
                                <span class="field-icon">⏱️</span>
                                <span class="field-label">المدة:</span>
                                <span class="field-value">${duration}</span>
                            </div>
                            <div class="course-field">
                                <span class="field-icon">📊</span>
                                <span class="field-label">المستوى:</span>
                                <span class="field-value">
                                    <span class="level-badge level-${level}">${level}</span>
                                </span>
                            </div>
                            <div class="course-field">
                                <span class="field-icon">💰</span>
                                <span class="field-label">السعر:</span>
                                <span class="field-value">
                                    <span class="price-tag">${price}</span>
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getFieldValue(fields, fieldName) {
            if (!fields || !fields[fieldName]) return null;
            
            const field = fields[fieldName];
            
            if (field.stringValue !== undefined) return field.stringValue;
            if (field.integerValue !== undefined) return field.integerValue;
            if (field.doubleValue !== undefined) return field.doubleValue;
            if (field.booleanValue !== undefined) return field.booleanValue;
            
            return null;
        }

        function showError(message) {
            const errorContainer = document.getElementById('error-container');
            errorContainer.innerHTML = `
                <div class="error">
                    <h3>❌ حدث خطأ في تحميل البيانات</h3>
                    <p>${message}</p>
                    <button class="refresh-btn" onclick="loadCourses()" style="margin-top: 15px;">
                        🔄 إعادة المحاولة
                    </button>
                </div>
            `;
            errorContainer.style.display = 'block';
        }

        function toggleRawData() {
            const rawData = document.getElementById('raw-data');
            rawDataVisible = !rawDataVisible;
            
            if (rawDataVisible) {
                rawData.style.display = 'block';
                document.querySelector('.toggle-raw').textContent = '🙈 إخفاء البيانات الخام';
            } else {
                rawData.style.display = 'none';
                document.querySelector('.toggle-raw').textContent = '📄 عرض البيانات الخام';
            }
        }

        function updateRawData(data) {
            const jsonViewer = document.getElementById('json-viewer');
            jsonViewer.textContent = JSON.stringify(data, null, 2);
        }
    </script>
</body>
</html>
