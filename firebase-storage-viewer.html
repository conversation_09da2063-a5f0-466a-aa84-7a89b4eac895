<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗂️ عارض Firebase Storage</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn.refresh {
            background: #28a745;
        }

        .btn.refresh:hover {
            background: #218838;
        }

        .stats {
            padding: 20px;
            background: #e8f5e8;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .content {
            padding: 20px;
        }

        .search-box {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .file-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
        }

        .file-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .file-icon {
            font-size: 3em;
            text-align: center;
            margin-bottom: 15px;
        }

        .file-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            word-break: break-all;
        }

        .file-info {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .file-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .file-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .download-btn {
            background: #28a745;
            color: white;
        }

        .download-btn:hover {
            background: #218838;
        }

        .info-btn {
            background: #17a2b8;
            color: white;
        }

        .info-btn:hover {
            background: #138496;
        }

        .copy-btn {
            background: #ffc107;
            color: #212529;
        }

        .copy-btn:hover {
            background: #e0a800;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .folder-card {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border: none;
        }

        .folder-card .file-icon {
            color: #d63031;
        }

        .image-preview {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .file-size {
            color: #28a745;
            font-weight: bold;
        }

        .file-date {
            color: #6c757d;
            font-size: 0.8em;
        }

        .breadcrumb {
            background: #e9ecef;
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
            margin: 0 5px;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ عارض Firebase Storage</h1>
            <p>استكشاف ملفات Firebase Storage للمشروع: story-e81c8</p>
        </div>

        <div class="controls">
            <button class="btn refresh" onclick="loadStorageFiles()">
                🔄 تحديث
            </button>
            <button class="btn" onclick="loadStorageInfo()">
                📊 معلومات التخزين
            </button>
            <a href="https://console.firebase.google.com/project/story-e81c8/storage" 
               target="_blank" class="btn">
                🌐 فتح Firebase Console
            </a>
        </div>

        <div class="stats" id="stats" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="total-files">0</div>
                <div class="stat-label">إجمالي الملفات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-size">0 KB</div>
                <div class="stat-label">الحجم الإجمالي</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="file-types">0</div>
                <div class="stat-label">أنواع الملفات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="folders">0</div>
                <div class="stat-label">المجلدات</div>
            </div>
        </div>

        <div class="content">
            <div class="breadcrumb" id="breadcrumb" style="display: none;">
                <span>📁 المسار: </span>
                <a href="#" onclick="navigateToFolder('')">الجذر</a>
            </div>

            <input type="text" class="search-box" id="search-box" 
                   placeholder="🔍 البحث في الملفات..." 
                   onkeyup="filterFiles()">

            <div id="loading" class="loading">
                <div class="loading-spinner"></div>
                <p>جاري تحميل ملفات Firebase Storage...</p>
            </div>

            <div id="error-message" style="display: none;"></div>
            <div id="success-message" style="display: none;"></div>

            <div class="files-grid" id="files-grid"></div>
        </div>
    </div>

    <script>
        let allFiles = [];
        let currentFolder = '';
        let filteredFiles = [];

        // تحميل ملفات Storage عند تحميل الصفحة
        window.onload = function() {
            loadStorageFiles();
        };

        async function loadStorageFiles() {
            showLoading(true);
            hideMessages();

            try {
                console.log('🗂️ تحميل ملفات Firebase Storage...');

                const response = await fetch('http://localhost:3000/api/test-firebase-storage');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('📊 بيانات Storage:', data);

                if (data.results && data.results.length > 0) {
                    // البحث عن النتيجة الناجحة التي تحتوي على البيانات
                    const successfulResult = data.results.find(r => r.success && r.data && r.data.items);
                    
                    if (successfulResult && successfulResult.data.items) {
                        allFiles = successfulResult.data.items;
                        filteredFiles = [...allFiles];
                        
                        displayFiles();
                        updateStats();
                        showStats(true);
                        showSuccess(`تم تحميل ${allFiles.length} ملف بنجاح من Firebase Storage`);
                    } else {
                        throw new Error('لم يتم العثور على ملفات في Storage');
                    }
                } else {
                    throw new Error('فشل في الحصول على بيانات Storage');
                }

            } catch (error) {
                console.error('خطأ في تحميل ملفات Storage:', error);
                showError(`فشل في تحميل ملفات Storage: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        function displayFiles() {
            const grid = document.getElementById('files-grid');
            
            if (filteredFiles.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 50px; color: #666;">
                        📭 لا توجد ملفات للعرض
                    </div>
                `;
                return;
            }

            grid.innerHTML = filteredFiles.map(file => {
                const fileName = file.name.split('/').pop();
                const filePath = file.name;
                const fileExtension = fileName.split('.').pop().toLowerCase();
                const fileIcon = getFileIcon(fileExtension);
                const fileSize = formatFileSize(file.size);
                const fileDate = formatDate(file.timeCreated);
                const downloadUrl = `https://firebasestorage.googleapis.com/v0/b/${file.bucket}/o/${encodeURIComponent(file.name)}?alt=media`;

                return `
                    <div class="file-card">
                        <div class="file-icon">${fileIcon}</div>
                        <div class="file-name">${fileName}</div>
                        <div class="file-info">📁 المسار: ${filePath}</div>
                        <div class="file-info file-size">📏 الحجم: ${fileSize}</div>
                        <div class="file-info file-date">📅 التاريخ: ${fileDate}</div>
                        <div class="file-actions">
                            <button class="file-btn download-btn" onclick="downloadFile('${downloadUrl}', '${fileName}')">
                                ⬇️ تحميل
                            </button>
                            <button class="file-btn info-btn" onclick="showFileInfo('${file.name}')">
                                ℹ️ تفاصيل
                            </button>
                            <button class="file-btn copy-btn" onclick="copyUrl('${downloadUrl}')">
                                📋 نسخ الرابط
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getFileIcon(extension) {
            const icons = {
                // صور
                'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'bmp': '🖼️', 'svg': '🖼️',
                // فيديو
                'mp4': '🎥', 'avi': '🎥', 'mov': '🎥', 'wmv': '🎥', 'flv': '🎥', 'webm': '🎥',
                // صوت
                'mp3': '🎵', 'wav': '🎵', 'flac': '🎵', 'aac': '🎵', 'ogg': '🎵',
                // مستندات
                'pdf': '📄', 'doc': '📝', 'docx': '📝', 'txt': '📝', 'rtf': '📝',
                'xls': '📊', 'xlsx': '📊', 'csv': '📊',
                'ppt': '📊', 'pptx': '📊',
                // أرشيف
                'zip': '🗜️', 'rar': '🗜️', '7z': '🗜️', 'tar': '🗜️', 'gz': '🗜️',
                // تطبيقات
                'apk': '📱', 'exe': '💻', 'msi': '💻', 'deb': '💻', 'rpm': '💻',
                // كود
                'html': '🌐', 'css': '🎨', 'js': '⚡', 'json': '📋', 'xml': '📋',
                'php': '🐘', 'py': '🐍', 'java': '☕', 'cpp': '⚙️', 'c': '⚙️'
            };
            
            return icons[extension] || '📄';
        }

        function formatFileSize(bytes) {
            if (!bytes) return 'غير محدد';
            
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            if (!dateString) return 'غير محدد';
            
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG') + ' ' + date.toLocaleTimeString('ar-EG');
        }

        function updateStats() {
            const totalFiles = allFiles.length;
            const totalSize = allFiles.reduce((sum, file) => sum + (parseInt(file.size) || 0), 0);
            const fileTypes = new Set(allFiles.map(file => file.name.split('.').pop().toLowerCase())).size;
            const folders = new Set(allFiles.map(file => file.name.split('/').slice(0, -1).join('/')).filter(f => f)).size;

            document.getElementById('total-files').textContent = totalFiles;
            document.getElementById('total-size').textContent = formatFileSize(totalSize);
            document.getElementById('file-types').textContent = fileTypes;
            document.getElementById('folders').textContent = folders;
        }

        function filterFiles() {
            const searchTerm = document.getElementById('search-box').value.toLowerCase();
            
            if (searchTerm === '') {
                filteredFiles = [...allFiles];
            } else {
                filteredFiles = allFiles.filter(file => 
                    file.name.toLowerCase().includes(searchTerm)
                );
            }
            
            displayFiles();
        }

        function downloadFile(url, filename) {
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.target = '_blank';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            showSuccess(`بدء تحميل الملف: ${filename}`);
        }

        function copyUrl(url) {
            navigator.clipboard.writeText(url).then(() => {
                showSuccess('تم نسخ رابط الملف إلى الحافظة');
            }).catch(err => {
                console.error('فشل في نسخ الرابط:', err);
                showError('فشل في نسخ الرابط');
            });
        }

        function showFileInfo(fileName) {
            const file = allFiles.find(f => f.name === fileName);
            if (!file) return;

            const info = `
📄 اسم الملف: ${file.name}
📏 الحجم: ${formatFileSize(file.size)}
📅 تاريخ الإنشاء: ${formatDate(file.timeCreated)}
📅 تاريخ التحديث: ${formatDate(file.updated)}
🔗 الرابط: ${file.mediaLink}
🆔 المعرف: ${file.generation}
            `;
            
            alert(info);
        }

        async function loadStorageInfo() {
            try {
                const response = await fetch('http://localhost:3000/api/test-firebase-storage');
                const data = await response.json();
                
                const info = `
📊 معلومات Firebase Storage:
🗂️ اسم الحاوية: ${data.storageBucket}
📅 وقت الاختبار: ${new Date(data.testTime).toLocaleString('ar-EG')}
✅ الروابط العاملة: ${data.summary.successful}/${data.summary.totalTested}
❌ الروابط المعطلة: ${data.summary.failed}

🔗 روابط مفيدة:
• Firebase Console: ${data.storageLinks.console}
• قواعد الأمان: ${data.storageLinks.rules}
                `;
                
                alert(info);
            } catch (error) {
                showError('فشل في جلب معلومات Storage');
            }
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showStats(show) {
            document.getElementById('stats').style.display = show ? 'grid' : 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.innerHTML = `<div class="error">❌ ${message}</div>`;
            errorDiv.style.display = 'block';
            setTimeout(() => errorDiv.style.display = 'none', 5000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.innerHTML = `<div class="success">✅ ${message}</div>`;
            successDiv.style.display = 'block';
            setTimeout(() => successDiv.style.display = 'none', 3000);
        }

        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }
    </script>
</body>
</html>
