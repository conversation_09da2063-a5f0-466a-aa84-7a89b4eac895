# تطبيق تسجيل الدخول مع خادم وسيط

هذا تطبيق بسيط يستخدم Firebase للمصادقة وقاعدة البيانات، مع خادم وسيط Node.js لتجاوز قيود CORS ومشكلة حظر المجال المحلي.

## المميزات

- تسجيل الدخول باستخدام البريد الإلكتروني فقط (بدون كلمة مرور)
- إنشاء حساب جديد
- عرض بيانات المستخدم من قاعدة البيانات
- خادم وسيط لتجاوز قيود CORS

## متطلبات التشغيل

- Node.js (الإصدار 12 أو أحدث)
- npm (مدير حزم Node.js)
- متصفح ويب حديث

## كيفية التشغيل

### 1. تثبيت اعتماديات الخادم الوسيط

```bash
npm install
```

### 2. تشغيل الخادم الوسيط

```bash
node server.js
```

سيعمل الخادم الوسيط على المنفذ 3000. يمكنك الوصول إليه على http://localhost:3000

### 3. تشغيل خادم الويب المحلي

يمكنك استخدام أي خادم ويب محلي لتشغيل التطبيق. على سبيل المثال، يمكنك استخدام خادم Python البسيط:

```bash
python -m http.server 8000
```

### 4. فتح التطبيق في المتصفح

افتح المتصفح وانتقل إلى http://localhost:8000

## كيفية الاستخدام

1. في صفحة تسجيل الدخول، أدخل البريد الإلكتروني `<EMAIL>` وانقر على زر "تسجيل الدخول بالبريد الإلكتروني"
2. سيتم تسجيل دخولك وتوجيهك إلى صفحة الترحيب
3. في صفحة الترحيب، ستظهر بيانات المستخدم المجلوبة من قاعدة البيانات (من خلال الخادم الوسيط)
4. يمكنك تسجيل الخروج بالنقر على زر "تسجيل الخروج"

## ملاحظات هامة

- هذا التطبيق يستخدم خادم وسيط لتجاوز قيود CORS ومشكلة حظر المجال المحلي في Firebase
- الخادم الوسيط يقوم بمحاكاة الاتصال بقاعدة بيانات Firebase للمستخدم `<EMAIL>`
- في بيئة الإنتاج، يجب تكوين Firebase لقبول الطلبات من المجال الخاص بك
- هذا المشروع لأغراض تعليمية فقط ولا ينبغي استخدامه في بيئة الإنتاج بدون تعديلات أمنية مناسبة

## التقنيات المستخدمة

- HTML
- CSS
- JavaScript
- Firebase Authentication
- Node.js
- Express.js
