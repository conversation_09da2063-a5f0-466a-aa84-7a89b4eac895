// دالة مساعدة للحصول على قيمة حقل من كائن Firestore
function getFieldValue(fields, fieldName) {
    // التحقق من وجود الحقل
    if (!fields || !fields[fieldName]) {
        console.log(`الحقل ${fieldName} غير موجود في:`, fields);
        return null;
    }

    const field = fields[fieldName];
    console.log(`قراءة الحقل ${fieldName}:`, field);

    // استخراج القيمة حسب نوعها
    if (field.stringValue !== undefined) {
        return field.stringValue;
    } else if (field.integerValue !== undefined) {
        return field.integerValue;
    } else if (field.doubleValue !== undefined) {
        return field.doubleValue;
    } else if (field.booleanValue !== undefined) {
        return field.booleanValue;
    } else if (field.timestampValue !== undefined) {
        return field.timestampValue;
    } else if (field.arrayValue !== undefined) {
        return field.arrayValue.values ? field.arrayValue.values.map(v => getFieldValue({ value: v }, 'value')) : [];
    } else if (field.mapValue !== undefined) {
        const result = {};
        if (field.mapValue.fields) {
            Object.keys(field.mapValue.fields).forEach(key => {
                result[key] = getFieldValue(field.mapValue.fields, key);
            });
        }
        return result;
    }

    console.log(`نوع غير معروف للحقل ${fieldName}:`, field);
    return null;
}

// دالة للاتصال بقاعدة بيانات Firebase Firestore من خلال الخادم الوسيط
function getUserData(email) {
    return new Promise((resolve, reject) => {
        // بناء عنوان URL للوصول إلى الخادم الوسيط
        const encodedEmail = encodeURIComponent(email);
        const url = `http://localhost:3000/api/users/${encodedEmail}`;

        console.log("محاولة الاتصال بالخادم الوسيط:", url);

        // إرسال طلب للحصول على بيانات المستخدم
        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log("استجابة الخادم:", response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`فشل الاتصال بالخادم الوسيط: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log("بيانات المستخدم من قاعدة البيانات:", data);

            // تحويل بيانات Firestore إلى كائن JavaScript
            const userData = {
                fullName: getFieldValue(data, 'fullName') || 'مستخدم جديد',
                age: getFieldValue(data, 'age') || 'غير متوفر',
                country: getFieldValue(data, 'country') || 'غير متوفر',
                interests: getFieldValue(data, 'interests') || 'غير متوفر',
                registrationDate: getFieldValue(data, 'registrationDate') || new Date().toLocaleDateString('ar-EG')
            };

            resolve(userData);
        })
        .catch(error => {
            console.error("خطأ في جلب بيانات المستخدم:", error);

            // في حالة الفشل، نرفض الوعد مع رسالة الخطأ
            reject(error);
        });
    });
}

// دالة لإنشاء أو تحديث بيانات المستخدم في قاعدة البيانات من خلال الخادم الوسيط
function saveUserData(email, userData) {
    return new Promise((resolve, reject) => {
        // بناء عنوان URL للوصول إلى الخادم الوسيط
        const encodedEmail = encodeURIComponent(email);
        const url = `http://localhost:3000/api/users/${encodedEmail}`;

        console.log("محاولة حفظ بيانات المستخدم:", email);

        // تحويل بيانات المستخدم إلى تنسيق Firestore
        const firestoreData = {
            fields: {
                fullName: { stringValue: userData.fullName || 'مستخدم جديد' },
                age: { stringValue: userData.age ? userData.age.toString() : 'غير متوفر' },
                country: { stringValue: userData.country || 'غير متوفر' },
                interests: { stringValue: userData.interests || 'غير متوفر' },
                registrationDate: { stringValue: userData.registrationDate || new Date().toLocaleDateString('ar-EG') },
                email: { stringValue: email }
            }
        };

        // إرسال طلب لحفظ بيانات المستخدم
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(firestoreData)
        })
        .then(response => {
            console.log("استجابة الخادم:", response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`فشل حفظ بيانات المستخدم: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log("تم حفظ بيانات المستخدم بنجاح:", data);
            resolve({ success: true });
        })
        .catch(error => {
            console.error("خطأ في حفظ بيانات المستخدم:", error);
            // في حالة الفشل، نرفض الوعد مع رسالة الخطأ
            reject(error);
        });
    });
}

// دالة لتسجيل الدخول باستخدام البريد الإلكتروني فقط من خلال الخادم الوسيط
function loginWithEmail(email) {
    return new Promise((resolve, reject) => {
        console.log("محاولة تسجيل الدخول بالبريد الإلكتروني:", email);

        // التحقق من صحة البريد الإلكتروني
        if (!email || !email.includes('@')) {
            reject(new Error('يرجى إدخال بريد إلكتروني صالح'));
            return;
        }

        // بناء عنوان URL للوصول إلى الخادم الوسيط
        const url = `http://localhost:3000/api/login`;

        // إرسال طلب لتسجيل الدخول
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
        })
        .then(response => {
            console.log("استجابة الخادم:", response.status, response.statusText);
            return response.json();
        })
        .then(data => {
            console.log("بيانات الاستجابة:", data);
            if (data.success) {
                resolve(data);
            } else {
                reject(new Error(data.message || 'فشل تسجيل الدخول'));
            }
        })
        .catch(error => {
            console.error("خطأ في تسجيل الدخول:", error);
            // في حالة الفشل، نرفض الوعد مع رسالة الخطأ
            reject(error);
        });
    });
}

// دالة للحصول على قائمة الكورسات من خلال الخادم الوسيط
function getCourses() {
    return new Promise((resolve, reject) => {
        console.log("محاولة الحصول على قائمة الكورسات");

        // بناء عنوان URL للوصول إلى الخادم الوسيط
        const url = `http://localhost:3000/api/courses`;

        // إرسال طلب للحصول على قائمة الكورسات
        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log("استجابة الخادم:", response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`فشل الحصول على قائمة الكورسات: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log("بيانات الكورسات:", data);

            // تحويل بيانات Firestore إلى مصفوفة من الكورسات
            let courses = [];

            if (data && data.documents) {
                console.log("عدد المستندات:", data.documents.length);

                courses = data.documents.map((doc, index) => {
                    console.log(`معالجة المستند ${index + 1}:`, doc);
                    const fields = doc.fields || {};
                    console.log(`حقول المستند ${index + 1}:`, fields);

                    const courseData = {
                        id: doc.name ? doc.name.split('/').pop() : '',
                        title: getFieldValue(fields, 'title'),
                        description: getFieldValue(fields, 'description'),
                        instructor: getFieldValue(fields, 'instructor'),
                        duration: getFieldValue(fields, 'duration'),
                        level: getFieldValue(fields, 'level'),
                        price: getFieldValue(fields, 'price')
                    };

                    console.log(`بيانات الكورس ${index + 1} النهائية:`, courseData);
                    return courseData;
                });
            }

            console.log("جميع الكورسات النهائية:", courses);

            resolve(courses);
        })
        .catch(error => {
            console.error("خطأ في الحصول على قائمة الكورسات:", error);

            // في حالة الفشل، نرفض الوعد مع رسالة الخطأ
            reject(error);
        });
    });
}

// دالة لإضافة كورس جديد
function addCourse(courseData) {
    return new Promise((resolve, reject) => {
        console.log("محاولة إضافة كورس جديد:", courseData);

        // التحقق من صحة بيانات الكورس
        if (!courseData.title) {
            reject(new Error('عنوان الكورس مطلوب'));
            return;
        }

        // تحويل بيانات الكورس إلى تنسيق Firestore
        const firestoreData = {
            fields: {
                title: { stringValue: courseData.title },
                description: { stringValue: courseData.description || '' },
                instructor: { stringValue: courseData.instructor || '' },
                duration: { stringValue: courseData.duration || '' },
                level: { stringValue: courseData.level || '' },
                price: { stringValue: courseData.price || 'مجاني' }
            }
        };

        // بناء عنوان URL للوصول إلى الخادم الوسيط
        const url = `http://localhost:3000/api/courses`;

        // إرسال طلب لإضافة الكورس
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(firestoreData)
        })
        .then(response => {
            console.log("استجابة الخادم:", response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`فشل إضافة الكورس: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log("تم إضافة الكورس بنجاح:", data);
            resolve(data);
        })
        .catch(error => {
            console.error("خطأ في إضافة الكورس:", error);
            reject(error);
        });
    });
}
